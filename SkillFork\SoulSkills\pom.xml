<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>shyrcs</groupId>
    <artifactId>soulskills</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
    </properties>

    <repositories>
        <repository>
            <id>papermc</id>
            <url>https://repo.papermc.io/repository/maven-public/</url>
        </repository>
        <repository>
            <id>md5-repo</id>
            <url>https://repo.md-5.net/content/repositories/public/</url>
        </repository>
        <repository>
            <id>phoenix</id>
            <url>https://nexus.phoenixdevt.fr/repository/maven-public/</url>
        </repository>
        <repository>
            <id>bg-repo</id>
            <url>https://repo.bg-software.com/repository/api/</url>
        </repository>
            <repository>
            <id>sk89q-repo</id>
            <url>https://maven.enginehub.org/repo/</url>
        </repository>

    </repositories>

    <dependencies>
        <dependency>
            <groupId>io.papermc.paper</groupId>
            <artifactId>paper-api</artifactId>
            <version>1.21.4-R0.1-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>net.kyori</groupId>
            <artifactId>adventure-api</artifactId>
            <version>4.21.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>io.lumine</groupId>
            <artifactId>MythicLib-dist</artifactId>
            <version>1.6.2-SNAPSHOT</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>net.Indyuce</groupId>
            <artifactId>MMOItems-API</artifactId>
            <version>6.9.5-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>net.Indyuce</groupId>
            <artifactId>MMOCore-API</artifactId>
            <version>1.12.1-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>

        <!-- WorldGuard API -->
        <dependency>
            <groupId>com.sk89q.worldguard</groupId>
            <artifactId>worldguard-bukkit</artifactId>
            <version>7.0.9</version>
            <scope>provided</scope>
        </dependency>

        <!-- SuperiorSkyblock2 API -->
        <dependency>
            <groupId>com.bgsoftware</groupId>
            <artifactId>SuperiorSkyblockAPI</artifactId>
            <version>2025.1</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>copy-jar</id>
                        <phase>package</phase>
                        <configuration>
                            <target>
                                <copy file="${project.build.directory}/${project.build.finalName}.jar"
                                      todir="C:\Users\<USER>\Desktop\Survival Project - MC\plugins" overwrite="true"/>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>