package shyrcs.shopkeeper.utils;

import org.bukkit.inventory.ItemStack;
import org.bukkit.Material;

import net.Indyuce.mmoitems.MMOItems;
import net.Indyuce.mmoitems.api.Type;
import net.Indyuce.mmoitems.api.item.mmoitem.MMOItem;
import net.Indyuce.mmoitems.api.item.template.MMOItemTemplate;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;

import java.util.logging.Logger;

/**
 * Utility class for working with MMOItems
 */
public class MMOItemUtils {
    
    private static final Logger logger = Logger.getLogger("SoulShopkeeper");
    
    /**
     * Creates an MMOItem by type and ID
     * @param type The MMOItem type (e.g., "SWORD", "CONSUMABLE")
     * @param id The MMOItem ID
     * @return The created ItemStack or null if failed
     */
    public static ItemStack createMMOItem(String type, String id) {
        try {
            Type mmoType = Type.get(type);
            if (mmoType == null) {
                logger.warning("Unknown MMOItem type: " + type);
                return null;
            }
            
            MMOItemTemplate template = MMOItems.plugin.getTemplates().getTemplate(mmoType, id);
            if (template == null) {
                logger.warning("MMOItem template not found: " + type + "." + id);
                return null;
            }
            
            MMOItem mmoItem = template.newBuilder().build();
            return mmoItem.newBuilder().build();
            
        } catch (Exception e) {
            logger.severe("Failed to create MMOItem " + type + "." + id + ": " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Gets the MMOItem type from an ItemStack
     * @param itemStack The ItemStack to check
     * @return The MMOItem type or null if not an MMOItem
     */
    public static String getMMOItemType(ItemStack itemStack) {
        if (itemStack == null || itemStack.getType() == Material.AIR) {
            return null;
        }

        return MMOItems.getTypeName(itemStack);
    }
    
    /**
     * Gets the MMOItem ID from an ItemStack
     * @param itemStack The ItemStack to check
     * @return The MMOItem ID or null if not an MMOItem
     */
    public static String getMMOItemId(ItemStack itemStack) {
        if (itemStack == null || itemStack.getType() == Material.AIR) {
            return null;
        }
        
        return MMOItems.getID(itemStack);
    }
    
    /**
     * Checks if an ItemStack is an MMOItem
     * @param itemStack The ItemStack to check
     * @return True if it's an MMOItem, false otherwise
     */
    public static boolean isMMOItem(ItemStack itemStack) {
        return getMMOItemType(itemStack) != null && getMMOItemId(itemStack) != null;
    }
    
    /**
     * Gets the full MMOItem identifier (type.id)
     * @param itemStack The ItemStack to check
     * @return The full identifier or null if not an MMOItem
     */
    public static String getMMOItemFullId(ItemStack itemStack) {
        String type = getMMOItemType(itemStack);
        String id = getMMOItemId(itemStack);
        
        if (type != null && id != null) {
            return type + "." + id;
        }
        
        return null;
    }
    
    /**
     * Creates a demonstration method for storing and retrieving MMOItems
     * This shows how the NBT preservation system works
     */
    public static void demonstrateMMOItemStorage(SoulShopkeeperPlugin plugin) {
        logger.info("=== MMOItem Storage Demonstration ===");
        
        // Example: Create a sample MMOItem (you would replace this with actual MMOItems)
        ItemStack sampleItem = createMMOItem("SWORD", "IRON_SWORD");
        
        if (sampleItem != null) {
            logger.info("Created sample MMOItem: " + getMMOItemFullId(sampleItem));
            
            // Store the item
            String storageId = plugin.getMMOItemStorage().storeMMOItem(sampleItem);
            if (storageId != null) {
                logger.info("Stored MMOItem with ID: " + storageId);
                
                // Retrieve the item
                ItemStack retrievedItem = plugin.getMMOItemStorage().retrieveMMOItem(storageId);
                if (retrievedItem != null) {
                    logger.info("Successfully retrieved MMOItem: " + getMMOItemFullId(retrievedItem));
                    logger.info("NBT data preserved: " + retrievedItem.equals(sampleItem));
                } else {
                    logger.warning("Failed to retrieve stored MMOItem");
                }
            } else {
                logger.warning("Failed to store MMOItem");
            }
        } else {
            logger.warning("Failed to create sample MMOItem - make sure MMOItems plugin is loaded");
        }
        
        logger.info("=== End Demonstration ===");
    }
    
    /**
     * Validates that an MMOItem still exists in the MMOItems plugin
     * This is useful for checking if items are still valid after server restarts
     */
    public static boolean validateMMOItem(String type, String id) {
        try {
            Type mmoType = Type.get(type);
            if (mmoType == null) {
                return false;
            }
            
            MMOItemTemplate template = MMOItems.plugin.getTemplates().getTemplate(mmoType, id);
            return template != null;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Gets a user-friendly display name for an MMOItem
     */
    public static String getDisplayName(ItemStack itemStack) {
        if (!isMMOItem(itemStack)) {
            return itemStack.getType().name().toLowerCase().replace('_', ' ');
        }
        
        // Check if item has custom display name
        if (itemStack.hasItemMeta() && itemStack.getItemMeta().hasDisplayName()) {
            return MessageUtils.stripColors(itemStack.getItemMeta().getDisplayName());
        }
        
        // Fallback to MMOItem ID
        String fullId = getMMOItemFullId(itemStack);
        return fullId != null ? fullId : "Unknown MMOItem";
    }
    
    /**
     * Creates a copy of an MMOItem with preserved NBT data
     * This is useful for ensuring item integrity
     */
    public static ItemStack cloneMMOItem(ItemStack original) {
        if (!isMMOItem(original)) {
            return original.clone();
        }
        
        // For MMOItems, we want to ensure all NBT data is preserved
        // This method would use the storage system to ensure perfect cloning
        SoulShopkeeperPlugin plugin = SoulShopkeeperPlugin.getInstance();
        if (plugin != null) {
            String tempId = plugin.getMMOItemStorage().storeMMOItem(original);
            if (tempId != null) {
                ItemStack cloned = plugin.getMMOItemStorage().retrieveMMOItem(tempId);
                // Clean up temporary storage
                // (In a real implementation, you might want to add a cleanup method)
                return cloned;
            }
        }
        
        // Fallback to regular clone
        return original.clone();
    }
}
