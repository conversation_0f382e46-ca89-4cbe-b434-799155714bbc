package shyrcs.shopkeeper.storage.database;

import org.bukkit.inventory.ItemStack;
import org.bukkit.configuration.ConfigurationSection;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.MMOItemStorage;

import java.util.Map;

/**
 * Wrapper class to make SQLiteMMOItemStorage compatible with the original MMOItemStorage interface
 */
public class DatabaseMMOItemStorageWrapper extends MMOItemStorage {

    private final SQLiteMMOItemStorage sqliteStorage;

    public DatabaseMMOItemStorageWrapper(SQLiteMMOItemStorage sqliteStorage) {
        super(SoulShopkeeperPlugin.getInstance());
        this.sqliteStorage = sqliteStorage;
    }
    
    @Override
    public String storeMMOItem(ItemStack itemStack) {
        return sqliteStorage.storeMMOItem(itemStack);
    }
    
    @Override
    public ItemStack retrieveMMOItem(String storageId) {
        return sqliteStorage.retrieveMMOItem(storageId);
    }
    
    @Override
    public boolean isMMOItem(ItemStack itemStack) {
        // Use the parent implementation
        return super.isMMOItem(itemStack);
    }
    
    @Override
    public String getMMOItemInfo(ItemStack itemStack) {
        // Use the parent implementation
        return super.getMMOItemInfo(itemStack);
    }
    
    @Override
    public void clearCache() {
        sqliteStorage.clearCache();
    }
    
    @Override
    public Map<String, Integer> getCacheStats() {
        return sqliteStorage.getCacheStats();
    }
    
    @Override
    public void saveToStorage(ConfigurationSection section) {
        // Database storage doesn't need this method, but we implement it for compatibility
        // This could be used for exporting data to YAML if needed
    }
    
    @Override
    public void loadFromStorage(ConfigurationSection section) {
        // Database storage doesn't need this method, but we implement it for compatibility
        // This could be used for importing data from YAML if needed
    }
    
    /**
     * Gets the underlying SQLite storage for direct access if needed
     */
    public SQLiteMMOItemStorage getSQLiteStorage() {
        return sqliteStorage;
    }
}
