package shyrcs.discordbot.top.managers;

import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import shyrcs.discordbot.top.SbmagicTopPlugin;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.logging.Logger;

public class ScheduledUpdateManager {
    
    private final SbmagicTopPlugin plugin;
    private final Logger logger;
    private final WebhookManager webhookManager;
    
    private BukkitTask updateTask;
    private boolean isEnabled;
    private int intervalMinutes;
    private boolean updateOnStartup;
    private boolean updateOnReload;
    
    public ScheduledUpdateManager(SbmagicTopPlugin plugin, WebhookManager webhookManager) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.webhookManager = webhookManager;
        
        loadConfig();
    }
    
    public void loadConfig() {
        this.isEnabled = plugin.getConfig().getBoolean("settings.auto-update.enabled", true);
        this.intervalMinutes = plugin.getConfig().getInt("settings.auto-update.interval", 60);
        this.updateOnStartup = plugin.getConfig().getBoolean("settings.auto-update.on-startup", true);
        this.updateOnReload = plugin.getConfig().getBoolean("settings.auto-update.on-reload", true);
        
        // Validate interval (minimum 5 minutes to avoid spam)
        if (intervalMinutes < 5) {
            logger.warning("Update interval too low (" + intervalMinutes + " minutes), setting to 5 minutes");
            intervalMinutes = 5;
        }
        
        logger.info("Auto-update config loaded: enabled=" + isEnabled + 
                   ", interval=" + intervalMinutes + "min" +
                   ", on-startup=" + updateOnStartup + 
                   ", on-reload=" + updateOnReload);
    }
    
    public void start() {
        if (!isEnabled) {
            logger.info("Auto-update is disabled in config");
            return;
        }
        
        if (!webhookManager.isWebhookEnabled()) {
            logger.info("Webhook is disabled, auto-update will not start");
            return;
        }
        
        // Stop existing task if running
        stop();
        
        // Schedule the update task
        long intervalTicks = intervalMinutes * 60 * 20L; // Convert minutes to ticks (20 ticks = 1 second)
        
        updateTask = new BukkitRunnable() {
            @Override
            public void run() {
                performScheduledUpdate();
            }
        }.runTaskTimerAsynchronously(plugin, intervalTicks, intervalTicks);
        
        logger.info("Started auto-update scheduler with " + intervalMinutes + " minute interval");
        
        // Perform startup update if enabled
        if (updateOnStartup) {
            Bukkit.getScheduler().runTaskLaterAsynchronously(plugin, () -> {
                logger.info("Performing startup webhook update...");
                performScheduledUpdate();
            }, 100L); // Delay 5 seconds after startup
        }
    }
    
    public void stop() {
        if (updateTask != null && !updateTask.isCancelled()) {
            updateTask.cancel();
            updateTask = null;
            logger.info("Stopped auto-update scheduler");
        }
    }
    
    public void restart() {
        logger.info("Restarting auto-update scheduler...");
        loadConfig();
        start();
    }
    
    public void triggerManualUpdate() {
        if (!webhookManager.isWebhookEnabled()) {
            logger.warning("Cannot trigger manual update: webhook is disabled");
            return;
        }
        
        logger.info("Triggering manual webhook update...");
        
        Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
            performScheduledUpdate();
        });
    }
    
    public void triggerReloadUpdate() {
        if (!updateOnReload) {
            logger.info("Reload update is disabled in config");
            return;
        }
        
        if (!webhookManager.isWebhookEnabled()) {
            logger.info("Cannot trigger reload update: webhook is disabled");
            return;
        }
        
        logger.info("Triggering reload webhook update...");
        
        Bukkit.getScheduler().runTaskLaterAsynchronously(plugin, () -> {
            performScheduledUpdate();
        }, 40L); // Delay 2 seconds after reload
    }
    
    private void performScheduledUpdate() {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            logger.info("[" + timestamp + "] Starting scheduled webhook update...");
            
            // Update all tops via webhook
            webhookManager.updateAllTops();
            
            logger.info("[" + timestamp + "] Completed scheduled webhook update");
            
        } catch (Exception e) {
            logger.severe("Error during scheduled update: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public boolean isEnabled() {
        return isEnabled;
    }
    
    public int getIntervalMinutes() {
        return intervalMinutes;
    }
    
    public boolean isUpdateOnStartup() {
        return updateOnStartup;
    }
    
    public boolean isUpdateOnReload() {
        return updateOnReload;
    }
    
    public boolean isRunning() {
        return updateTask != null && !updateTask.isCancelled();
    }
    
    public String getStatus() {
        StringBuilder status = new StringBuilder();
        status.append("Auto-update Status:\n");
        status.append("- Enabled: ").append(isEnabled).append("\n");
        status.append("- Webhook Enabled: ").append(webhookManager.isWebhookEnabled()).append("\n");
        status.append("- Running: ").append(isRunning()).append("\n");
        status.append("- Interval: ").append(intervalMinutes).append(" minutes\n");
        status.append("- Update on startup: ").append(updateOnStartup).append("\n");
        status.append("- Update on reload: ").append(updateOnReload).append("\n");
        
        if (isRunning()) {
            long nextUpdateTicks = updateTask.getTaskId();
            status.append("- Next update in: ~").append(intervalMinutes).append(" minutes");
        }
        
        return status.toString();
    }
    
    /**
     * Get next update time estimate
     */
    public String getNextUpdateTime() {
        if (!isRunning()) {
            return "Not scheduled";
        }
        
        LocalDateTime nextUpdate = LocalDateTime.now().plusMinutes(intervalMinutes);
        return nextUpdate.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
    }
    
    /**
     * Force update with custom delay
     */
    public void scheduleUpdate(long delaySeconds) {
        if (!webhookManager.isWebhookEnabled()) {
            logger.warning("Cannot schedule update: webhook is disabled");
            return;
        }
        
        logger.info("Scheduling webhook update in " + delaySeconds + " seconds...");
        
        Bukkit.getScheduler().runTaskLaterAsynchronously(plugin, () -> {
            performScheduledUpdate();
        }, delaySeconds * 20L);
    }
}
