package shyrcs.Ability;

import io.lumine.mythic.lib.api.item.ItemTag;
import io.lumine.mythic.lib.api.item.SupportedNBTTagValues;
import net.Indyuce.mmoitems.api.item.build.ItemStackBuilder;
import net.Indyuce.mmoitems.api.item.mmoitem.ReadMMOItem;
import net.Indyuce.mmoitems.api.util.NumericStatFormula;
import net.Indyuce.mmoitems.gui.edition.EditionInventory;
import net.Indyuce.mmoitems.stat.data.DoubleData;
import net.Indyuce.mmoitems.stat.type.DoubleStat;
import org.bukkit.Material;
import org.bukkit.event.inventory.InventoryClickEvent;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Custom MMOItems stat để tăng tỷ lệ drop item từ MythicMob
 */
public class MythicDropBoostStat extends DoubleStat {
    
    public MythicDropBoostStat() {
        super("MYTHIC_DROP_BOOST",
              Material.DRAGON_EGG,
              "Mythic Drop Boost",
              new String[]{
                  "Tăng tỷ lệ drop item từ MythicMob.",
                  "Áp dụng cho tất cả drops của mob.",
                  "",
                  "Đơn vị: % (phần trăm)"
              },
              new String[]{"weapon", "tool", "armor", "accessory", "all"}
        );
    }
    
    @Override
    public void whenApplied(ItemStackBuilder item, DoubleData data) {
        // Lưu giá trị vào NBT
        item.addItemTag(new ItemTag("MMOITEMS_MYTHIC_DROP_BOOST", data.getValue()));
        
        // Thêm lore
        item.getLore().insert("mythic-drop-boost", 
            "§7Mythic Drop Boost: §a" + String.format("%.1f", data.getValue()) + "%");
    }
    
    @Override
    public ArrayList<ItemTag> getAppliedNBT(DoubleData data) {
        ArrayList<ItemTag> tags = new ArrayList<>();
        tags.add(new ItemTag("MMOITEMS_MYTHIC_DROP_BOOST", data.getValue()));
        return tags;
    }
    
    @Override
    public void whenDisplayed(List<String> lore, Optional<NumericStatFormula> statData) {
        if (statData.isPresent()) {
            lore.add("§7Mythic Drop Boost: §a" + statData.get().toString() + "%");
        } else {
            lore.add("§7Mythic Drop Boost: §c0%");
        }
    }
    
    @Override
    public DoubleData getLoadedNBT(ArrayList<ItemTag> tags) {
        ItemTag tag = ItemTag.getTagAtPath("MMOITEMS_MYTHIC_DROP_BOOST", tags);
        return tag != null ? new DoubleData((Double) tag.getValue()) : null;
    }
    
    @Override
    public void whenLoaded(ReadMMOItem mmoItem) {
        // Get tags - Following the pattern from other stats
        ArrayList<ItemTag> relevantTags = new ArrayList<>();

        if (mmoItem.getNBT().hasTag("MMOITEMS_MYTHIC_DROP_BOOST"))
            relevantTags.add(ItemTag.getTagAtPath("MMOITEMS_MYTHIC_DROP_BOOST", mmoItem.getNBT(), SupportedNBTTagValues.DOUBLE));

        DoubleData data = getLoadedNBT(relevantTags);

        // Valid?
        if (data != null) {
            // Set
            mmoItem.setData(this, data);
        }
    }
    
    @Override
    public void whenInput(EditionInventory inv, String message, Object... info) {
        try {
            double value = Double.parseDouble(message);
            if (value < 0) {
                inv.getPlayer().sendMessage("§cGiá trị phải lớn hơn hoặc bằng 0!");
                return;
            }
            if (value > 1000) {
                inv.getPlayer().sendMessage("§cGiá trị không nên vượt quá 1000%!");
                return;
            }
            
            inv.getEditedSection().set(getPath(), value);
            inv.registerTemplateEdition();
            inv.getPlayer().sendMessage("§aMythic Drop Boost đã được đặt thành " + value + "%");
        } catch (NumberFormatException e) {
            inv.getPlayer().sendMessage("§cVui lòng nhập một số hợp lệ!");
        }
    }
    
    @Override
    public void whenClicked(EditionInventory inv, InventoryClickEvent event) {
        // Sử dụng hệ thống StatEdition của MMOItems
        new net.Indyuce.mmoitems.api.edition.StatEdition(inv, this)
            .enable("&eNhập giá trị Mythic Drop Boost (%):",
                    "&7Tăng tỷ lệ drop item từ MythicMob",
                    "&7Ví dụ: 50 = tăng 50% drop rate");
    }
    
    @Override
    public DoubleData getClearStatData() {
        return new DoubleData(0);
    }
}
