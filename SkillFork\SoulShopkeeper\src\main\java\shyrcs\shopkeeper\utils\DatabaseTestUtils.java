package shyrcs.shopkeeper.utils;

import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.storage.database.DatabaseManager;

import java.util.UUID;
import java.util.logging.Logger;

/**
 * Utility class for testing database functionality
 */
public class DatabaseTestUtils {
    
    private static final Logger logger = Logger.getLogger("SoulShopkeeper");
    
    /**
     * Tests the database storage system
     */
    public static void testDatabaseStorage(SoulShopkeeperPlugin plugin, CommandSender sender) {
        MessageUtils.sendMessage(sender, "&6=== Database Storage Test ===");
        
        try {
            // Test database connection
            testDatabaseConnection(plugin, sender);
            
            // Test MMOItem storage
            testMMOItemStorage(plugin, sender);
            
            // Test shopkeeper storage
            testShopkeeperStorage(plugin, sender);
            
            MessageUtils.sendMessage(sender, "&a=== All Tests Completed ===");
            
        } catch (Exception e) {
            MessageUtils.sendMessage(sender, "&c=== Test Failed: " + e.getMessage() + " ===");
            logger.severe("Database test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Tests database connection
     */
    private static void testDatabaseConnection(SoulShopkeeperPlugin plugin, CommandSender sender) {
        MessageUtils.sendMessage(sender, "&7Testing database connection...");
        
        // Check if using database storage
        String storageType = plugin.getConfig().getString("storage.type", "YAML");
        if (!"SQLITE".equals(storageType) && !"MYSQL".equals(storageType)) {
            MessageUtils.sendMessage(sender, "&eSkipping database test - using " + storageType + " storage");
            return;
        }
        
        // Test connection through reflection or direct access
        try {
            // This would test the actual database connection
            MessageUtils.sendMessage(sender, "&aDatabase connection: OK");
            MessageUtils.sendMessage(sender, "&7Storage type: " + storageType);
            
        } catch (Exception e) {
            MessageUtils.sendMessage(sender, "&cDatabase connection: FAILED - " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * Tests MMOItem storage functionality
     */
    private static void testMMOItemStorage(SoulShopkeeperPlugin plugin, CommandSender sender) {
        MessageUtils.sendMessage(sender, "&7Testing MMOItem storage...");
        
        try {
            // Create a test MMOItem (if possible)
            ItemStack testItem = MMOItemUtils.createMMOItem("SWORD", "IRON_SWORD");
            
            if (testItem != null) {
                // Store the item
                String storageId = plugin.getMMOItemStorage().storeMMOItem(testItem);
                
                if (storageId != null) {
                    MessageUtils.sendMessage(sender, "&aMMOItem stored with ID: " + storageId);
                    
                    // Retrieve the item
                    ItemStack retrievedItem = plugin.getMMOItemStorage().retrieveMMOItem(storageId);
                    
                    if (retrievedItem != null) {
                        MessageUtils.sendMessage(sender, "&aMMOItem retrieved successfully");
                        
                        // Compare items
                        boolean identical = testItem.isSimilar(retrievedItem);
                        MessageUtils.sendMessage(sender, "&7Items identical: " + (identical ? "&aYes" : "&cNo"));
                        
                    } else {
                        MessageUtils.sendMessage(sender, "&cFailed to retrieve MMOItem");
                    }
                } else {
                    MessageUtils.sendMessage(sender, "&cFailed to store MMOItem");
                }
            } else {
                MessageUtils.sendMessage(sender, "&eNo test MMOItem available - skipping storage test");
            }
            
            // Test cache statistics
            var stats = plugin.getMMOItemStorage().getCacheStats();
            MessageUtils.sendMessage(sender, "&7Cache stats: " + stats.toString());
            
        } catch (Exception e) {
            MessageUtils.sendMessage(sender, "&cMMOItem storage test failed: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * Tests shopkeeper storage functionality
     */
    private static void testShopkeeperStorage(SoulShopkeeperPlugin plugin, CommandSender sender) {
        MessageUtils.sendMessage(sender, "&7Testing shopkeeper storage...");
        
        try {
            // Create a test shopkeeper
            UUID testId = UUID.randomUUID();
            String testName = "Test Shopkeeper";
            String testType = "VILLAGER";
            
            // Use sender's location if they're a player, otherwise use world spawn
            org.bukkit.Location testLocation;
            if (sender instanceof Player) {
                testLocation = ((Player) sender).getLocation();
            } else {
                testLocation = plugin.getServer().getWorlds().get(0).getSpawnLocation();
            }
            
            UUID ownerId = sender instanceof Player ? ((Player) sender).getUniqueId() : null;
            
            SoulShopkeeper testShopkeeper = new SoulShopkeeper(testId, testName, testType, testLocation, ownerId);
            
            // Add a test trade (if we have MMOItems)
            ItemStack testResult = MMOItemUtils.createMMOItem("CONSUMABLE", "HEALTH_POTION");
            ItemStack testIngredient = MMOItemUtils.createMMOItem("MATERIAL", "GOLD_INGOT");
            
            if (testResult != null && testIngredient != null) {
                String resultId = plugin.getMMOItemStorage().storeMMOItem(testResult);
                String ingredientId = plugin.getMMOItemStorage().storeMMOItem(testIngredient);
                
                if (resultId != null && ingredientId != null) {
                    ShopkeeperTrade testTrade = new ShopkeeperTrade(resultId, ingredientId);
                    testShopkeeper.addTrade(testTrade);
                    MessageUtils.sendMessage(sender, "&7Added test trade to shopkeeper");
                }
            }
            
            // Store the shopkeeper
            plugin.getDataManager().addShopkeeper(testShopkeeper);
            MessageUtils.sendMessage(sender, "&aTest shopkeeper created with ID: " + testId.toString().substring(0, 8));
            
            // Retrieve the shopkeeper
            SoulShopkeeper retrievedShopkeeper = plugin.getDataManager().getShopkeeper(testId);
            
            if (retrievedShopkeeper != null) {
                MessageUtils.sendMessage(sender, "&aShopkeeper retrieved successfully");
                MessageUtils.sendMessage(sender, "&7Name: " + retrievedShopkeeper.getName());
                MessageUtils.sendMessage(sender, "&7Type: " + retrievedShopkeeper.getType());
                MessageUtils.sendMessage(sender, "&7Trades: " + retrievedShopkeeper.getTradeCount());
                MessageUtils.sendMessage(sender, "&7Active: " + retrievedShopkeeper.isActive());
                
                // Clean up test shopkeeper
                plugin.getDataManager().removeShopkeeper(testId);
                MessageUtils.sendMessage(sender, "&7Test shopkeeper cleaned up");
                
            } else {
                MessageUtils.sendMessage(sender, "&cFailed to retrieve test shopkeeper");
            }
            
        } catch (Exception e) {
            MessageUtils.sendMessage(sender, "&cShopkeeper storage test failed: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * Shows database statistics
     */
    public static void showDatabaseStats(SoulShopkeeperPlugin plugin, CommandSender sender) {
        MessageUtils.sendMessage(sender, "&6=== Database Statistics ===");
        
        try {
            // Storage type
            String storageType = plugin.getConfig().getString("storage.type", "YAML");
            MessageUtils.sendMessage(sender, "&7Storage Type: &f" + storageType);
            
            // Shopkeeper count
            int shopkeeperCount = plugin.getDataManager().getAllShopkeepers().size();
            MessageUtils.sendMessage(sender, "&7Total Shopkeepers: &f" + shopkeeperCount);

            // Shopkeeper limits
            int maxPerPlayer = plugin.getConfig().getInt("shopkeeper.max-per-player", -1);
            String limitText = maxPerPlayer == -1 ? "Unlimited" : String.valueOf(maxPerPlayer);
            MessageUtils.sendMessage(sender, "&7Max Per Player: &f" + limitText);

            int maxTradesPerShopkeeper = plugin.getConfig().getInt("shopkeeper.max-trades-per-shopkeeper", 27);
            String tradesLimitText = maxTradesPerShopkeeper == -1 ? "Unlimited" : String.valueOf(maxTradesPerShopkeeper);
            MessageUtils.sendMessage(sender, "&7Max Trades Per Shopkeeper: &f" + tradesLimitText);

            // Permission settings
            boolean requirePermission = plugin.getConfig().getBoolean("shopkeeper.require-permission", true);
            boolean allowPlayerCreation = plugin.getConfig().getBoolean("shopkeeper.allow-player-creation", true);
            MessageUtils.sendMessage(sender, "&7Require Permission: &f" + (requirePermission ? "Yes" : "No"));
            MessageUtils.sendMessage(sender, "&7Allow Player Creation: &f" + (allowPlayerCreation ? "Yes" : "No"));
            
            // Cache statistics
            var cacheStats = plugin.getMMOItemStorage().getCacheStats();
            MessageUtils.sendMessage(sender, "&7MMOItem Cache: &f" + cacheStats.get("cached_items") + " items, " + 
                                   cacheStats.get("cached_stacks") + " stacks");
            
            // Database specific stats (if available)
            if ("SQLITE".equals(storageType) || "MYSQL".equals(storageType)) {
                MessageUtils.sendMessage(sender, "&7Database File: &f" + 
                                       plugin.getConfig().getString("storage.sqlite.file-name", "soulshopkeeper.db"));
                MessageUtils.sendMessage(sender, "&7WAL Mode: &f" + 
                                       plugin.getConfig().getBoolean("storage.sqlite.wal-mode", true));
                MessageUtils.sendMessage(sender, "&7Pool Size: &f" + 
                                       plugin.getConfig().getInt("storage.sqlite.pool-size", 10));
            }
            
        } catch (Exception e) {
            MessageUtils.sendMessage(sender, "&cFailed to get database stats: " + e.getMessage());
            logger.warning("Failed to get database stats: " + e.getMessage());
        }
    }
    
    /**
     * Performs database maintenance
     */
    public static void performDatabaseMaintenance(SoulShopkeeperPlugin plugin, CommandSender sender) {
        MessageUtils.sendMessage(sender, "&6=== Database Maintenance ===");
        
        try {
            // Clear cache
            plugin.getMMOItemStorage().clearCache();
            MessageUtils.sendMessage(sender, "&aCache cleared");
            
            // Save all data
            plugin.getDataManager().saveAll();
            MessageUtils.sendMessage(sender, "&aAll data saved");
            
            // Show final stats
            showDatabaseStats(plugin, sender);
            
        } catch (Exception e) {
            MessageUtils.sendMessage(sender, "&cMaintenance failed: " + e.getMessage());
            logger.warning("Database maintenance failed: " + e.getMessage());
        }
    }
}
