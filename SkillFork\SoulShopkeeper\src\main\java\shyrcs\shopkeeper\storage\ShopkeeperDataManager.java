package shyrcs.shopkeeper.storage;

import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.Location;
import org.bukkit.Bukkit;
import org.bukkit.World;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * Manages shopkeeper data storage and retrieval
 */
public class ShopkeeperDataManager {
    
    private final SoulShopkeeperPlugin plugin;
    private final Logger logger;
    private final File dataFile;
    private YamlConfiguration dataConfig;
    
    // Cache for shopkeepers
    private final Map<UUID, SoulShopkeeper> shopkeepers;
    private final Map<Location, UUID> locationIndex;
    private final Map<UUID, Set<UUID>> playerShopkeepers;
    
    public ShopkeeperDataManager(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.dataFile = new File(plugin.getDataFolder(), plugin.getConfig().getString("storage.file-name", "shopkeepers.yml"));
        this.shopkeepers = new ConcurrentHashMap<>();
        this.locationIndex = new ConcurrentHashMap<>();
        this.playerShopkeepers = new ConcurrentHashMap<>();
        
        loadData();
        
        // Start auto-save task
        startAutoSaveTask();
    }
    
    /**
     * Loads shopkeeper data from file
     */
    private void loadData() {
        try {
            if (!dataFile.exists()) {
                dataFile.getParentFile().mkdirs();
                dataFile.createNewFile();
            }
            
            dataConfig = YamlConfiguration.loadConfiguration(dataFile);
            
            // Load shopkeepers
            ConfigurationSection shopkeepersSection = dataConfig.getConfigurationSection("shopkeepers");
            if (shopkeepersSection != null) {
                for (String uuidString : shopkeepersSection.getKeys(false)) {
                    try {
                        UUID uuid = UUID.fromString(uuidString);
                        ConfigurationSection shopkeeperSection = shopkeepersSection.getConfigurationSection(uuidString);
                        
                        SoulShopkeeper shopkeeper = loadShopkeeper(shopkeeperSection);
                        if (shopkeeper != null) {
                            shopkeepers.put(uuid, shopkeeper);
                            locationIndex.put(shopkeeper.getLocation(), uuid);
                            
                            // Update player index
                            UUID ownerId = shopkeeper.getOwnerId();
                            if (ownerId != null) {
                                playerShopkeepers.computeIfAbsent(ownerId, k -> new HashSet<>()).add(uuid);
                            }
                        }
                    } catch (Exception e) {
                        logger.warning("Failed to load shopkeeper " + uuidString + ": " + e.getMessage());
                    }
                }
            }
            
            // Load MMOItems storage
            ConfigurationSection itemsSection = dataConfig.getConfigurationSection("mmoitems");
            if (itemsSection != null) {
                plugin.getMMOItemStorage().loadFromStorage(itemsSection);
            }
            
            logger.info("Loaded " + shopkeepers.size() + " shopkeepers from storage");
            
        } catch (Exception e) {
            logger.severe("Failed to load shopkeeper data: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Loads a single shopkeeper from configuration
     */
    private SoulShopkeeper loadShopkeeper(ConfigurationSection section) {
        try {
            UUID id = UUID.fromString(section.getName());
            String name = section.getString("name");
            String type = section.getString("type", "VILLAGER");
            
            // Load location
            Location location = loadLocation(section.getConfigurationSection("location"));
            if (location == null) {
                logger.warning("Invalid location for shopkeeper " + id);
                return null;
            }
            
            // Load owner
            UUID ownerId = null;
            String ownerString = section.getString("owner");
            if (ownerString != null) {
                try {
                    ownerId = UUID.fromString(ownerString);
                } catch (IllegalArgumentException e) {
                    // Invalid UUID, continue without owner
                }
            }
            
            // Create shopkeeper
            SoulShopkeeper shopkeeper = new SoulShopkeeper(id, name, type, location, ownerId);
            
            // Load trades
            ConfigurationSection tradesSection = section.getConfigurationSection("trades");
            if (tradesSection != null) {
                for (String tradeKey : tradesSection.getKeys(false)) {
                    ConfigurationSection tradeSection = tradesSection.getConfigurationSection(tradeKey);
                    ShopkeeperTrade trade = loadTrade(tradeSection);
                    if (trade != null) {
                        shopkeeper.addTrade(trade);
                    }
                }
            }
            
            return shopkeeper;
            
        } catch (Exception e) {
            logger.warning("Failed to load shopkeeper: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Loads a location from configuration
     */
    private Location loadLocation(ConfigurationSection section) {
        if (section == null) return null;
        
        try {
            String worldName = section.getString("world");
            World world = Bukkit.getWorld(worldName);
            if (world == null) {
                logger.warning("World not found: " + worldName);
                return null;
            }
            
            double x = section.getDouble("x");
            double y = section.getDouble("y");
            double z = section.getDouble("z");
            float yaw = (float) section.getDouble("yaw", 0.0);
            float pitch = (float) section.getDouble("pitch", 0.0);
            
            return new Location(world, x, y, z, yaw, pitch);
            
        } catch (Exception e) {
            logger.warning("Failed to load location: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Loads a trade from configuration
     */
    private ShopkeeperTrade loadTrade(ConfigurationSection section) {
        try {
            String resultItemId = section.getString("result");
            String ingredient1Id = section.getString("ingredient1");
            String ingredient2Id = section.getString("ingredient2");
            
            int maxUses = section.getInt("maxUses", -1);
            int uses = section.getInt("uses", 0);
            boolean enabled = section.getBoolean("enabled", true);
            
            return new ShopkeeperTrade(resultItemId, ingredient1Id, ingredient2Id, maxUses, uses, enabled);
            
        } catch (Exception e) {
            logger.warning("Failed to load trade: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Saves all data to file
     */
    public void saveAll() {
        try {
            if (dataConfig == null) {
                dataConfig = new YamlConfiguration();
            }
            
            // Clear existing data
            dataConfig.set("shopkeepers", null);
            dataConfig.set("mmoitems", null);
            
            // Save shopkeepers
            ConfigurationSection shopkeepersSection = dataConfig.createSection("shopkeepers");
            for (Map.Entry<UUID, SoulShopkeeper> entry : shopkeepers.entrySet()) {
                ConfigurationSection shopkeeperSection = shopkeepersSection.createSection(entry.getKey().toString());
                saveShopkeeper(entry.getValue(), shopkeeperSection);
            }
            
            // Save MMOItems
            ConfigurationSection itemsSection = dataConfig.createSection("mmoitems");
            plugin.getMMOItemStorage().saveToStorage(itemsSection);
            
            // Save to file
            dataConfig.save(dataFile);
            
            if (plugin.getConfig().getBoolean("general.debug", false)) {
                logger.info("Saved " + shopkeepers.size() + " shopkeepers to storage");
            }
            
        } catch (IOException e) {
            logger.severe("Failed to save shopkeeper data: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Saves a single shopkeeper to configuration
     */
    private void saveShopkeeper(SoulShopkeeper shopkeeper, ConfigurationSection section) {
        section.set("name", shopkeeper.getName());
        section.set("type", shopkeeper.getType());
        
        if (shopkeeper.getOwnerId() != null) {
            section.set("owner", shopkeeper.getOwnerId().toString());
        }
        
        // Save location
        ConfigurationSection locationSection = section.createSection("location");
        saveLocation(shopkeeper.getLocation(), locationSection);
        
        // Save trades
        if (!shopkeeper.getTrades().isEmpty()) {
            ConfigurationSection tradesSection = section.createSection("trades");
            int index = 0;
            for (ShopkeeperTrade trade : shopkeeper.getTrades()) {
                ConfigurationSection tradeSection = tradesSection.createSection(String.valueOf(index++));
                saveTrade(trade, tradeSection);
            }
        }
    }
    
    /**
     * Saves a location to configuration
     */
    private void saveLocation(Location location, ConfigurationSection section) {
        section.set("world", location.getWorld().getName());
        section.set("x", location.getX());
        section.set("y", location.getY());
        section.set("z", location.getZ());
        section.set("yaw", location.getYaw());
        section.set("pitch", location.getPitch());
    }
    
    /**
     * Saves a trade to configuration
     */
    private void saveTrade(ShopkeeperTrade trade, ConfigurationSection section) {
        section.set("result", trade.getResultItemId());
        section.set("ingredient1", trade.getIngredient1Id());
        if (trade.getIngredient2Id() != null) {
            section.set("ingredient2", trade.getIngredient2Id());
        }
        section.set("maxUses", trade.getMaxUses());
        section.set("uses", trade.getUses());
        section.set("enabled", trade.isEnabled());
    }
    
    /**
     * Starts the auto-save task
     */
    private void startAutoSaveTask() {
        int interval = plugin.getConfig().getInt("general.auto-save-interval", 5) * 60 * 20; // Convert minutes to ticks
        
        Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            saveAll();
        }, interval, interval);
    }
    
    // Public API methods
    
    public void addShopkeeper(SoulShopkeeper shopkeeper) {
        shopkeepers.put(shopkeeper.getId(), shopkeeper);
        locationIndex.put(shopkeeper.getLocation(), shopkeeper.getId());
        
        if (shopkeeper.getOwnerId() != null) {
            playerShopkeepers.computeIfAbsent(shopkeeper.getOwnerId(), k -> new HashSet<>()).add(shopkeeper.getId());
        }
    }
    
    public void removeShopkeeper(UUID id) {
        SoulShopkeeper shopkeeper = shopkeepers.remove(id);
        if (shopkeeper != null) {
            locationIndex.remove(shopkeeper.getLocation());
            
            if (shopkeeper.getOwnerId() != null) {
                Set<UUID> playerShops = playerShopkeepers.get(shopkeeper.getOwnerId());
                if (playerShops != null) {
                    playerShops.remove(id);
                    if (playerShops.isEmpty()) {
                        playerShopkeepers.remove(shopkeeper.getOwnerId());
                    }
                }
            }
        }
    }
    
    public SoulShopkeeper getShopkeeper(UUID id) {
        return shopkeepers.get(id);
    }
    
    public SoulShopkeeper getShopkeeperAt(Location location) {
        UUID id = locationIndex.get(location);
        return id != null ? shopkeepers.get(id) : null;
    }
    
    public Collection<SoulShopkeeper> getAllShopkeepers() {
        return new ArrayList<>(shopkeepers.values());
    }
    
    public Collection<SoulShopkeeper> getPlayerShopkeepers(UUID playerId) {
        Set<UUID> shopIds = playerShopkeepers.get(playerId);
        if (shopIds == null) {
            return new ArrayList<>();
        }
        
        List<SoulShopkeeper> result = new ArrayList<>();
        for (UUID id : shopIds) {
            SoulShopkeeper shop = shopkeepers.get(id);
            if (shop != null) {
                result.add(shop);
            }
        }
        return result;
    }
    
    public int getPlayerShopkeeperCount(UUID playerId) {
        Set<UUID> shopIds = playerShopkeepers.get(playerId);
        return shopIds != null ? shopIds.size() : 0;
    }
}
