package shyrcs.discordbot.top.managers;

import net.dv8tion.jda.api.EmbedBuilder;
import org.bukkit.configuration.file.YamlConfiguration;
import shyrcs.discordbot.top.SbmagicTopPlugin;
import shyrcs.discordbot.top.models.TopConfig;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

public class WebhookManager {
    
    private final SbmagicTopPlugin plugin;
    private final Logger logger;
    private final File webhookDataFile;
    private YamlConfiguration webhookData;
    private final Map<String, String> messageIds; // topType -> messageId
    
    public WebhookManager(SbmagicTopPlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.webhookDataFile = new File(plugin.getDataFolder(), "webhook-data.yml");
        this.messageIds = new HashMap<>();
        
        loadWebhookData();
    }
    
    private void loadWebhookData() {
        if (!webhookDataFile.exists()) {
            try {
                webhookDataFile.createNewFile();
                webhookData = new YamlConfiguration();
                saveWebhookData();
            } catch (IOException e) {
                logger.severe("Failed to create webhook data file: " + e.getMessage());
                return;
            }
        }
        
        webhookData = YamlConfiguration.loadConfiguration(webhookDataFile);
        
        // Load message IDs
        if (webhookData.contains("message-ids")) {
            for (String key : webhookData.getConfigurationSection("message-ids").getKeys(false)) {
                messageIds.put(key, webhookData.getString("message-ids." + key));
            }
        }
        
        logger.info("Loaded " + messageIds.size() + " webhook message IDs");
    }
    
    private void saveWebhookData() {
        try {
            // Save message IDs
            for (Map.Entry<String, String> entry : messageIds.entrySet()) {
                webhookData.set("message-ids." + entry.getKey(), entry.getValue());
            }
            
            webhookData.save(webhookDataFile);
        } catch (IOException e) {
            logger.severe("Failed to save webhook data: " + e.getMessage());
        }
    }
    
    public boolean isWebhookEnabled() {
        boolean enabled = plugin.getConfig().getBoolean("discord.webhook.enabled", false);
        String url = getWebhookUrl();

        if (!enabled) {
            logger.info("Webhook is disabled in config (discord.webhook.enabled = false)");
            return false;
        }

        if (url.isEmpty() || url.equals("YOUR_WEBHOOK_URL_HERE")) {
            logger.warning("Webhook URL not configured properly: " + url);
            return false;
        }

        return true;
    }
    
    public String getWebhookUrl() {
        return plugin.getConfig().getString("discord.webhook.url", "");
    }
    

    
    public String getWebhookUsername() {
        return plugin.getConfig().getString("discord.webhook.username", "SkyBlock Leaderboard");
    }
    
    public String getWebhookAvatarUrl() {
        return plugin.getConfig().getString("discord.webhook.avatar-url", "");
    }
    
    public CompletableFuture<Void> sendOrUpdateTopWebhook(String topType) {
        if (!isWebhookEnabled()) {
            return CompletableFuture.completedFuture(null);
        }
        
        String webhookUrl = getWebhookUrl();
        if (webhookUrl.isEmpty() || webhookUrl.equals("YOUR_WEBHOOK_URL_HERE")) {
            logger.warning("Webhook URL not configured properly");
            return CompletableFuture.completedFuture(null);
        }
        
        TopConfig topConfig = plugin.getConfigManager().getTopConfig(topType);
        if (topConfig == null) {
            logger.warning("Top config not found for type: " + topType);
            return CompletableFuture.completedFuture(null);
        }
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Create embed
                EmbedBuilder embed = createTopEmbed(topConfig);
                
                // Check if we have existing message ID
                String existingMessageId = messageIds.get(topType);
                
                if (existingMessageId != null) {
                    // Try to edit existing message
                    return editWebhookMessage(webhookUrl, existingMessageId, embed, topType);
                } else {
                    // Send new message
                    return sendNewWebhookMessage(webhookUrl, embed, topType);
                }
                
            } catch (Exception e) {
                logger.severe("Error in webhook operation for " + topType + ": " + e.getMessage());
                return false;
            }
        }).thenAccept(success -> {
            if (success) {
                logger.info("Successfully updated webhook for " + topType);
            } else {
                logger.warning("Failed to update webhook for " + topType);
            }
        });
    }
    
    private EmbedBuilder createTopEmbed(TopConfig topConfig) {
        EmbedBuilder embed = new EmbedBuilder();
        embed.setTitle("🏆 " + topConfig.getName());
        embed.setColor(new Color(topConfig.getColor()));
        embed.setTimestamp(Instant.now());
        
        // Set thumbnail if available
        if (topConfig.getThumbnail() != null && !topConfig.getThumbnail().isEmpty()) {
            embed.setThumbnail(topConfig.getThumbnail());
        }
        
        // Set footer
        String footerText = plugin.getConfig().getString("embed.footer", "SkyBlock Magic - Leaderboard");
        embed.setFooter(footerText + " • Cập nhật tự động mỗi giờ");
        
        // Parse and add content
        StringBuilder description = new StringBuilder();
        if (topConfig.getDescription() != null && !topConfig.getDescription().isEmpty()) {
            description.append(topConfig.getDescription()).append("\n\n");
        }
        
        for (String line : topConfig.getContent()) {
            String parsedLine = plugin.getPlaceholderManager().parsePlaceholders(line);
            String discordLine = plugin.getPlaceholderManager().convertMinecraftToDiscord(parsedLine);
            description.append(discordLine).append("\n");
        }
        
        embed.setDescription(description.toString());
        
        return embed;
    }
    
    private boolean sendNewWebhookMessage(String webhookUrl, EmbedBuilder embed, String topType) {
        try {
            String jsonPayload = createWebhookPayload(embed, null);
            String response = sendHttpRequest(webhookUrl, "POST", jsonPayload);

            if (response != null && !response.isEmpty()) {
                // Parse response to get message ID
                String messageId = parseMessageIdFromResponse(response);
                if (messageId != null) {
                    messageIds.put(topType, messageId);
                    saveWebhookData();
                    logger.info("Sent new webhook message for " + topType + " with ID: " + messageId);
                }
            }

            return true;

        } catch (Exception e) {
            logger.severe("Failed to send new webhook message: " + e.getMessage());
            return false;
        }
    }
    
    private boolean editWebhookMessage(String webhookUrl, String messageId, EmbedBuilder embed, String topType) {
        try {
            String editUrl = webhookUrl + "/messages/" + messageId;
            String jsonPayload = createWebhookPayload(embed, messageId);
            String response = sendHttpRequest(editUrl, "PATCH", jsonPayload);

            if (response != null) {
                logger.info("Successfully edited webhook message for " + topType);
                return true;
            } else {
                throw new Exception("Empty response from Discord");
            }

        } catch (Exception e) {
            logger.warning("Failed to edit webhook message " + messageId + ", sending new message: " + e.getMessage());

            // Remove invalid message ID and try sending new message
            messageIds.remove(topType);
            saveWebhookData();

            return sendNewWebhookMessage(webhookUrl, embed, topType);
        }
    }
    
    public void updateAllTops() {
        if (!isWebhookEnabled()) {
            return;
        }
        
        logger.info("Starting webhook update for all tops...");
        
        Map<String, TopConfig> allConfigs = plugin.getConfigManager().getAllTopConfigs();
        
        for (String topType : allConfigs.keySet()) {
            sendOrUpdateTopWebhook(topType);
            
            // Add small delay between updates to avoid rate limiting
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        logger.info("Completed webhook update for " + allConfigs.size() + " tops");
    }
    
    public void clearMessageIds() {
        messageIds.clear();
        saveWebhookData();
        logger.info("Cleared all webhook message IDs");
    }
    
    public Map<String, String> getMessageIds() {
        return new HashMap<>(messageIds);
    }

    private String createWebhookPayload(EmbedBuilder embed, String messageId) {
        StringBuilder json = new StringBuilder();
        json.append("{");

        // Add username if not editing
        if (messageId == null) {
            String username = getWebhookUsername();
            if (!username.isEmpty()) {
                json.append("\"username\":\"").append(escapeJson(username)).append("\",");
            }

            String avatarUrl = getWebhookAvatarUrl();
            if (!avatarUrl.isEmpty()) {
                json.append("\"avatar_url\":\"").append(escapeJson(avatarUrl)).append("\",");
            }
        }

        // Add embeds
        json.append("\"embeds\":[");
        json.append(embedToJson(embed));
        json.append("]");

        json.append("}");
        return json.toString();
    }

    private String embedToJson(EmbedBuilder embed) {
        var builtEmbed = embed.build();
        StringBuilder json = new StringBuilder();
        json.append("{");

        // Title
        if (builtEmbed.getTitle() != null) {
            json.append("\"title\":\"").append(escapeJson(builtEmbed.getTitle())).append("\",");
        }

        // Description
        if (builtEmbed.getDescription() != null) {
            json.append("\"description\":\"").append(escapeJson(builtEmbed.getDescription())).append("\",");
        }

        // Color
        if (builtEmbed.getColor() != null) {
            json.append("\"color\":").append(builtEmbed.getColor().getRGB() & 0xFFFFFF).append(",");
        }

        // Timestamp
        if (builtEmbed.getTimestamp() != null) {
            json.append("\"timestamp\":\"").append(builtEmbed.getTimestamp().format(DateTimeFormatter.ISO_INSTANT)).append("\",");
        }

        // Footer
        if (builtEmbed.getFooter() != null) {
            json.append("\"footer\":{\"text\":\"").append(escapeJson(builtEmbed.getFooter().getText())).append("\"},");
        }

        // Thumbnail
        if (builtEmbed.getThumbnail() != null) {
            json.append("\"thumbnail\":{\"url\":\"").append(escapeJson(builtEmbed.getThumbnail().getUrl())).append("\"},");
        }

        // Remove trailing comma
        if (json.charAt(json.length() - 1) == ',') {
            json.setLength(json.length() - 1);
        }

        json.append("}");
        return json.toString();
    }

    private String escapeJson(String text) {
        if (text == null) return "";
        return text.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }

    private String sendHttpRequest(String urlString, String method, String jsonPayload) {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod(method);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("User-Agent", "SbmagicTop/1.0");
            connection.setDoOutput(true);

            // Send JSON payload
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            int responseCode = connection.getResponseCode();
            if (responseCode >= 200 && responseCode < 300) {
                // Read response
                try (var inputStream = connection.getInputStream()) {
                    return new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
                }
            } else {
                // Read error response
                try (var errorStream = connection.getErrorStream()) {
                    if (errorStream != null) {
                        String errorResponse = new String(errorStream.readAllBytes(), StandardCharsets.UTF_8);
                        logger.warning("HTTP " + responseCode + ": " + errorResponse);
                    }
                }
                return null;
            }

        } catch (Exception e) {
            logger.severe("HTTP request failed: " + e.getMessage());
            return null;
        }
    }

    private String parseMessageIdFromResponse(String response) {
        try {
            // Simple JSON parsing to extract message ID
            int idStart = response.indexOf("\"id\":\"") + 6;
            if (idStart > 5) {
                int idEnd = response.indexOf("\"", idStart);
                if (idEnd > idStart) {
                    return response.substring(idStart, idEnd);
                }
            }
        } catch (Exception e) {
            logger.warning("Failed to parse message ID from response: " + e.getMessage());
        }
        return null;
    }
}
