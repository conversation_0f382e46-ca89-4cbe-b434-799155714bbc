# 🐉 MYTHIC DROP BOOST - VÍ DỤ SỬ DỤNG

## 🎯 **<PERSON><PERSON><PERSON> Tạo Item Mẫu**

### **1. Mythic Slayer Sword - High Boost**
```bash
# Tạo item
/mmoitems create weapon mythic_slayer

# Edit trong GUI
/mmoitems edit weapon mythic_slayer
# Click "Mythic Drop Boost" → Nhập: 75

# Hoặc dùng lệnh (nếu có modify command)
/mmoitems modify weapon mythic_slayer set display-name "§6§lMythic Slayer"
/mmoitems modify weapon mythic_slayer set material NETHERITE_SWORD
/mmoitems modify weapon mythic_slayer add-lore "§7Tăng 75% drop rate từ MythicMob"
/mmoitems modify weapon mythic_slayer add-lore "§7Specialized for boss hunting"

# Lấy item
/mmoitems give <player> weapon mythic_slayer
```

### **2. Dragon Hunter Set - Balanced Boost**
```bash
# Helmet
/mmoitems create armor dragon_helmet
/mmoitems edit armor dragon_helmet
# Mythic Drop Boost: 20
# Display name: "§c§lDragon Hunter Helmet"
# Material: NETHERITE_HELMET

# Chestplate
/mmoitems create armor dragon_chest
/mmoitems edit armor dragon_chest  
# Mythic Drop Boost: 20
# Display name: "§c§lDragon Hunter Chestplate"
# Material: NETHERITE_CHESTPLATE

# Leggings
/mmoitems create armor dragon_legs
/mmoitems edit armor dragon_legs
# Mythic Drop Boost: 20
# Display name: "§c§lDragon Hunter Leggings"  
# Material: NETHERITE_LEGGINGS

# Boots
/mmoitems create armor dragon_boots
/mmoitems edit armor dragon_boots
# Mythic Drop Boost: 20
# Display name: "§c§lDragon Hunter Boots"
# Material: NETHERITE_BOOTS

# Total set bonus: 80% boost
```

### **3. Treasure Hunter Tools**
```bash
# Pickaxe với boost (cho mining MythicMobs)
/mmoitems create tool treasure_pick
/mmoitems edit tool treasure_pick
# Mythic Drop Boost: 40
# Display name: "§e§lTreasure Hunter Pickaxe"
# Material: DIAMOND_PICKAXE

# Axe với boost
/mmoitems create tool treasure_axe  
/mmoitems edit tool treasure_axe
# Mythic Drop Boost: 40
# Display name: "§e§lTreasure Hunter Axe"
# Material: DIAMOND_AXE
```

### **4. Accessory Set - Stack Boost**
```bash
# Ring of Greed
/mmoitems create accessory greed_ring
/mmoitems edit accessory greed_ring
# Mythic Drop Boost: 30
# Display name: "§a§lRing of Greed"
# Material: GOLD_INGOT

# Necklace of Fortune
/mmoitems create accessory fortune_necklace
/mmoitems edit accessory fortune_necklace  
# Mythic Drop Boost: 35
# Display name: "§a§lNecklace of Fortune"
# Material: EMERALD

# Lucky Charm
/mmoitems create accessory lucky_charm
/mmoitems edit accessory lucky_charm
# Mythic Drop Boost: 25
# Display name: "§a§lLucky Charm"
# Material: RABBIT_FOOT

# Total accessory boost: 90%
```

### **5. Boss Killer Bow - Ranged Boost**
```bash
/mmoitems create weapon boss_bow
/mmoitems edit weapon boss_bow
# Mythic Drop Boost: 60
# Display name: "§5§lBoss Killer Bow"
# Material: BOW
# Add lore: "§7Perfect for ranged MythicMob hunting"
# Add lore: "§7Increases drop rates by 60%"

/mmoitems give <player> weapon boss_bow
```

---

## 🎮 **Test Scenarios**

### **Scenario 1: Solo Hunter**
```bash
# Tạo complete solo build
/mmoitems give <player> weapon mythic_slayer    # 75% boost
/mmoitems give <player> accessory greed_ring    # 30% boost
/mmoitems give <player> accessory lucky_charm   # 25% boost

# Total: 130% boost
# Expected: ~2.3x drop rate increase
```

### **Scenario 2: Full Set Player**
```bash
# Give full dragon hunter set
/mmoitems give <player> armor dragon_helmet     # 20% boost
/mmoitems give <player> armor dragon_chest      # 20% boost  
/mmoitems give <player> armor dragon_legs       # 20% boost
/mmoitems give <player> armor dragon_boots      # 20% boost
/mmoitems give <player> weapon mythic_slayer    # 75% boost
/mmoitems give <player> accessory greed_ring    # 30% boost

# Total: 185% boost  
# Expected: ~2.85x drop rate increase
```

### **Scenario 3: Extreme Farmer**
```bash
# Max boost setup (for testing)
/mmoitems create weapon extreme_sword
/mmoitems edit weapon extreme_sword
# Mythic Drop Boost: 200

/mmoitems create armor extreme_helmet  
/mmoitems edit armor extreme_helmet
# Mythic Drop Boost: 100

# Total: 300% boost
# Expected: 4x drop rate increase
# WARNING: May cause server lag!
```

---

## 📊 **Drop Rate Testing**

### **Test Commands**
```bash
# Tạo test items với different boost levels
/mmoitems create weapon test_10
/mmoitems edit weapon test_10
# Mythic Drop Boost: 10

/mmoitems create weapon test_50  
/mmoitems edit weapon test_50
# Mythic Drop Boost: 50

/mmoitems create weapon test_100
/mmoitems edit weapon test_100  
# Mythic Drop Boost: 100

# Give to different players for comparison
/mmoitems give player1 weapon test_10
/mmoitems give player2 weapon test_50  
/mmoitems give player3 weapon test_100
```

### **Test Procedure**
1. **Setup test MythicMob** với known drop rates
2. **Give different boost items** to test players
3. **Kill same mob multiple times** (suggest 50+ kills)
4. **Compare drop results** between players
5. **Calculate actual boost effectiveness**

---

## 🔧 **Advanced Configurations**

### **Balanced PvE Setup**
```bash
# Low-tier boost (early game)
# Mythic Drop Boost: 15-25%

# Mid-tier boost (mid game)  
# Mythic Drop Boost: 30-50%

# High-tier boost (end game)
# Mythic Drop Boost: 60-100%

# Legendary boost (rare items)
# Mythic Drop Boost: 100-150%
```

### **Economy-Friendly Setup**
```bash
# Prevent economy inflation
# Cap total boost at 100% per player
# Use diminishing returns for stacking
# Balance with item rarity/cost
```

### **Performance-Optimized Setup**
```bash
# Limit concurrent boosted players
# Use lower boost values (under 100%)
# Monitor server TPS during events
# Consider boost cooldowns if needed
```

---

## 🎯 **Quick Start Guide**

### **For Server Admins**
```bash
# 1. Create basic boost weapon
/mmoitems create weapon starter_boost
/mmoitems edit weapon starter_boost
# Mythic Drop Boost: 25
# Give to new players

# 2. Create premium boost set
/mmoitems create weapon premium_boost  
/mmoitems edit weapon premium_boost
# Mythic Drop Boost: 75
# Sell in shop or reward

# 3. Test with MythicMob
# Spawn test mob and verify boost works
```

### **For Players**
```bash
# 1. Get boost item from admin/shop
/mmoitems give <yourself> weapon starter_boost

# 2. Find MythicMob to test
# Look for custom mobs (not vanilla)

# 3. Kill and observe drops
# Should see message: "[Mythic Drop Boost] Tăng X% drop rate!"

# 4. Compare with non-boost kills
# Notice increased drop amounts
```

---

## ⚠️ **Important Notes**

### **Server Performance**
- Test với ít players trước khi release
- Monitor TPS khi có nhiều boost items active
- Consider limiting max boost per player

### **Economy Balance**  
- Boost quá cao có thể phá vỡ economy
- Test drop rates carefully trước release
- Consider item durability/repair costs

### **MythicMob Compatibility**
- Chỉ hoạt động với MythicMobs plugin
- Không ảnh hưởng vanilla mob drops
- Test với different MythicMob versions

---

## 🚀 **Ready to Use!**

Mythic Drop Boost system đã sẵn sàng với:

- **✅ Complete stat implementation**
- **✅ Reflection-based MythicMob integration**  
- **✅ Stack support for multiple items**
- **✅ Performance optimized code**
- **✅ Comprehensive documentation**

**Next Steps:**
1. Restart server để load plugin mới
2. Test với example items above
3. Balance boost values cho server của bạn
4. Enjoy enhanced MythicMob farming! 🐉⚡
