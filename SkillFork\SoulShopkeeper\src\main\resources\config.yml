# SoulShopkeeper Configuration
# Independent shopkeeper plugin with MMOItems support and NBT preservation

# General Settings
general:
  # Language file to use
  language: "en"
  
  # Debug mode - shows additional information in console
  debug: false
  
  # Auto-save interval in minutes
  auto-save-interval: 5

  # Cleanup interval for old unused items (in hours)
  cleanup-interval: 24

# MMOItems Integration Settings
mmoitems:
  # Enable MMOItems integration
  enabled: true
  
  # Preserve NBT data when saving/loading items
  preserve-nbt: true
  
  # Cache MMOItems for better performance
  cache-items: true
  
  # Cache duration in minutes
  cache-duration: 30
  
  # Supported MMOItem types (leave empty for all types)
  supported-types: []

# Shopkeeper Settings
shopkeeper:
  # Default shopkeeper type
  default-type: "VILLAGER"

  # Maximum number of shopkeepers per player (-1 for unlimited)
  max-per-player: -1

  # Maximum number of trades per shopkeeper (-1 for unlimited)
  max-trades-per-shopkeeper: -1

  # Allow players to create shopkeepers
  allow-player-creation: true

  # Require permission to create shopkeepers (if false, anyone can create)
  require-permission: true

# GUI Settings
gui:
  # GUI title for shopkeeper editor
  editor-title: "&6&lSoulShopkeeper Editor"
  
  # GUI size (must be multiple of 9, max 54)
  editor-size: 54
  
  # Update GUI every X ticks (20 ticks = 1 second)
  update-interval: 20

# Entity settings
entity:
  # Default entity type for shopkeepers
  default-type: "VILLAGER"

  # Entity behavior
  look-at-players: true     # Smooth AI behavior to look at nearby players (disable if causing issues)

# Storage Settings
storage:
  # Storage type: YAML, SQLITE, or MYSQL
  type: "SQLITE"

  # File name for YAML storage
  yaml-file: "shopkeepers.yml"

  # SQLite settings (recommended for most servers)
  sqlite:
    file-name: "soulshopkeeper.db"
    # Enable WAL mode for better performance
    wal-mode: true
    # Connection pool size
    pool-size: 10
    # Connection timeout in seconds
    timeout: 30

  # MySQL settings (for large servers with multiple instances)
  mysql:
    host: "localhost"
    port: 3306
    database: "soulshopkeeper"
    username: "root"
    password: ""
    # Connection pool settings
    pool-size: 10
    max-lifetime: 1800000
    connection-timeout: 20000
    # Table prefix
    table-prefix: "ssk_"
    # Enable SSL
    use-ssl: false

# Messages
messages:
  prefix: "&8[&6SoulShopkeeper&8] "
  no-permission: "&cYou don't have permission to use this command!"
  player-only: "&cThis command can only be used by players!"
  shopkeeper-created: "&aShopkeeper created successfully!"
  shopkeeper-deleted: "&aShopkeeper deleted successfully!"
  shopkeeper-not-found: "&cShopkeeper not found!"
  max-shopkeepers-reached: "&cYou have reached the maximum number of shopkeepers! (&7Current limit: {limit}&c)"
  max-trades-reached: "&cThis shopkeeper has reached the maximum number of trades! (&7Current limit: {limit}&c)"
  creation-disabled: "&cShopkeeper creation is currently disabled!"
  invalid-mmoitem: "&cInvalid MMOItem specified!"
  item-saved: "&aMMOItem saved successfully!"
  item-loaded: "&aMMOItem loaded successfully!"
