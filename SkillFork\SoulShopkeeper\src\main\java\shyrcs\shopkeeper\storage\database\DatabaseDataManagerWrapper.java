package shyrcs.shopkeeper.storage.database;

import org.bukkit.Location;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.ShopkeeperDataManager;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;

import java.util.Collection;
import java.util.UUID;

/**
 * Wrapper class to make SQLiteShopkeeperDataManager compatible with the original ShopkeeperDataManager interface
 */
public class DatabaseDataManagerWrapper extends ShopkeeperDataManager {

    private final SQLiteShopkeeperDataManager sqliteDataManager;

    public DatabaseDataManagerWrapper(SQLiteShopkeeperDataManager sqliteDataManager) {
        super(SoulShopkeeperPlugin.getInstance());
        this.sqliteDataManager = sqliteDataManager;
    }
    
    @Override
    public void addShopkeeper(SoulShopkeeper shopkeeper) {
        sqliteDataManager.addShopkeeper(shopkeeper);
    }
    
    @Override
    public void removeShopkeeper(UUID id) {
        sqliteDataManager.removeShopkeeper(id);
    }
    
    @Override
    public SoulShopkeeper getShopkeeper(UUID id) {
        return sqliteDataManager.getShopkeeper(id);
    }
    
    @Override
    public SoulShopkeeper getShopkeeperAt(Location location) {
        return sqliteDataManager.getShopkeeperAt(location);
    }
    
    @Override
    public Collection<SoulShopkeeper> getAllShopkeepers() {
        return sqliteDataManager.getAllShopkeepers();
    }
    
    @Override
    public Collection<SoulShopkeeper> getPlayerShopkeepers(UUID playerId) {
        return sqliteDataManager.getPlayerShopkeepers(playerId);
    }
    
    @Override
    public int getPlayerShopkeeperCount(UUID playerId) {
        return sqliteDataManager.getPlayerShopkeeperCount(playerId);
    }
    
    @Override
    public void saveAll() {
        sqliteDataManager.saveAll();
    }
    
    /**
     * Gets the underlying SQLite data manager for direct access if needed
     */
    public SQLiteShopkeeperDataManager getSQLiteDataManager() {
        return sqliteDataManager;
    }
}
