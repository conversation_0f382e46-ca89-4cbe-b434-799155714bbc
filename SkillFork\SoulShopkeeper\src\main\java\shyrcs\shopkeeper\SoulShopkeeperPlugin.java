package shyrcs.shopkeeper;

import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.plugin.PluginManager;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandExecutor;
import org.bukkit.event.Listener;

import shyrcs.shopkeeper.commands.SoulShopkeeperCommand;
import shyrcs.shopkeeper.listeners.ShopkeeperListener;
import shyrcs.shopkeeper.storage.MMOItemStorage;
import shyrcs.shopkeeper.storage.ShopkeeperDataManager;
import shyrcs.shopkeeper.storage.database.DatabaseManager;
import shyrcs.shopkeeper.storage.database.SQLiteMMOItemStorage;
import shyrcs.shopkeeper.storage.database.SQLiteShopkeeperDataManager;
import shyrcs.shopkeeper.storage.database.DatabaseMMOItemStorageWrapper;
import shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper;
import shyrcs.shopkeeper.gui.ShopkeeperGUIManager;
import shyrcs.shopkeeper.entity.ShopkeeperEntityManager;

import net.Indyuce.mmoitems.MMOItems;
import net.Indyuce.mmoitems.api.Type;

import java.util.logging.Logger;

public class SoulShopkeeperPlugin extends JavaPlugin {

    private static SoulShopkeeperPlugin instance;
    private Logger logger;

    // Managers
    private MMOItemStorage mmoItemStorage;
    private ShopkeeperDataManager dataManager;
    private ShopkeeperGUIManager guiManager;
    private DatabaseManager databaseManager;
    private ShopkeeperEntityManager entityManager;

    // Database storage implementations
    private SQLiteMMOItemStorage sqliteMMOItemStorage;
    private SQLiteShopkeeperDataManager sqliteDataManager;

    // Plugin dependencies
    private MMOItems mmoItems;

    @Override
    public void onEnable() {
        instance = this;
        logger = getLogger();

        // Check dependencies
        if (!checkDependencies()) {
            getLogger().severe("Required dependencies not found! Disabling plugin...");
            Bukkit.getPluginManager().disablePlugin(this);
            return;
        }

        // Initialize managers
        initializeManagers();

        // Register commands
        registerCommands();

        // Register listeners
        registerListeners();

        // Load configuration
        saveDefaultConfig();

        // Spawn shopkeeper entities after everything is loaded
        Bukkit.getScheduler().runTaskLater(this, () -> {
            entityManager.spawnAllShopkeepers();
            entityManager.startCleanupTask();
        }, 20L); // Wait 1 second for world to be fully loaded

        logger.info("SoulShopkeeper has been enabled successfully!");
    }

    @Override
    public void onDisable() {
        // Save all data before shutdown
        if (dataManager != null) {
            dataManager.saveAll();
        }

        if (sqliteDataManager != null) {
            sqliteDataManager.saveAll();
        }

        // Remove all shopkeeper entities
        if (entityManager != null) {
            entityManager.shutdown();
        }

        // Close database connections
        if (databaseManager != null) {
            databaseManager.close();
        }

        logger.info("SoulShopkeeper has been disabled!");
    }

    private boolean checkDependencies() {
        PluginManager pm = Bukkit.getPluginManager();

        // Check MMOItems (required)
        if (!pm.isPluginEnabled("MMOItems")) {
            logger.severe("MMOItems plugin is required but not found!");
            return false;
        }
        mmoItems = MMOItems.plugin;

        return true;
    }

    private void initializeManagers() {
        String storageType = getConfig().getString("storage.type", "SQLITE").toUpperCase();

        if ("SQLITE".equals(storageType) || "MYSQL".equals(storageType)) {
            // Initialize database storage
            databaseManager = new DatabaseManager(this);

            if (databaseManager.initialize()) {
                logger.info("Using database storage: " + storageType);

                // Use database implementations
                sqliteMMOItemStorage = new SQLiteMMOItemStorage(this, databaseManager);
                sqliteDataManager = new SQLiteShopkeeperDataManager(this, databaseManager);

                // Create wrapper implementations for compatibility
                mmoItemStorage = new DatabaseMMOItemStorageWrapper(sqliteMMOItemStorage);
                dataManager = new DatabaseDataManagerWrapper(sqliteDataManager);
            } else {
                logger.warning("Failed to initialize database storage, falling back to YAML");
                initializeYAMLStorage();
            }
        } else {
            // Use YAML storage
            initializeYAMLStorage();
        }

        guiManager = new ShopkeeperGUIManager(this);
        entityManager = new ShopkeeperEntityManager(this);
    }

    private void initializeYAMLStorage() {
        logger.info("Using YAML storage");
        mmoItemStorage = new MMOItemStorage(this);
        dataManager = new ShopkeeperDataManager(this);
    }



    private void registerCommands() {
        getCommand("soulshopkeeper").setExecutor(new SoulShopkeeperCommand(this));
    }

    private void registerListeners() {
        PluginManager pm = Bukkit.getPluginManager();
        pm.registerEvents(new ShopkeeperListener(this), this);
        pm.registerEvents(new shyrcs.shopkeeper.gui.TradeEditGUIListener(this), this);
    }

    // Getters
    public static SoulShopkeeperPlugin getInstance() {
        return instance;
    }

    public MMOItemStorage getMMOItemStorage() {
        return mmoItemStorage;
    }

    public ShopkeeperDataManager getDataManager() {
        return dataManager;
    }

    public ShopkeeperGUIManager getGUIManager() {
        return guiManager;
    }

    public MMOItems getMMOItems() {
        return mmoItems;
    }

    public ShopkeeperEntityManager getEntityManager() {
        return entityManager;
    }
}