package shyrcs.shopkeeper.storage.data;

import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.Material;

import net.Indyuce.mmoitems.MMOItems;
import net.Indyuce.mmoitems.api.Type;
import net.Indyuce.mmoitems.api.item.mmoitem.MMOItem;
import net.Indyuce.mmoitems.api.item.mmoitem.VolatileMMOItem;

import java.util.*;
import java.util.logging.Logger;

/**
 * Represents a stored MMOItem with all its data preserved
 * This class handles the serialization and deserialization of MMOItems
 */
public class StoredMMOItem {
    
    private String type;
    private String itemId;
    private Material material;
    private int amount;
    private String displayName;
    private List<String> lore;
    private Map<String, Object> storedData;
    private String rawNBT;
    private long timestamp;
    
    // Private constructor - use factory methods
    private StoredMMOItem() {
        this.timestamp = System.currentTimeMillis();
        this.storedData = new HashMap<>();
    }

    // Constructor for database loading
    private StoredMMOItem(String type, String itemId, Material material, int amount,
                         String displayName, List<String> lore, Map<String, Object> storedData,
                         String rawNBT, long timestamp) {
        this.type = type;
        this.itemId = itemId;
        this.material = material;
        this.amount = amount;
        this.displayName = displayName;
        this.lore = lore != null ? new ArrayList<>(lore) : new ArrayList<>();
        this.storedData = storedData != null ? new HashMap<>(storedData) : new HashMap<>();
        this.rawNBT = rawNBT;
        this.timestamp = timestamp;
    }
    
    /**
     * Creates a StoredMMOItem from an ItemStack
     * @param itemStack The ItemStack to store
     * @return StoredMMOItem or null if not a valid MMOItem
     */
    public static StoredMMOItem fromItemStack(ItemStack itemStack) {
        if (itemStack == null || itemStack.getType().isAir()) {
            return null;
        }
        
        // Check if it's an MMOItem
        String mmoType = MMOItems.getTypeName(itemStack);
        String mmoId = MMOItems.getID(itemStack);

        // Handle both MMOItems and vanilla items
        if (mmoType == null || mmoId == null) {
            // Vanilla item - use material name as type and generate ID
            mmoType = "VANILLA";
            mmoId = itemStack.getType().name();
        }
        
        StoredMMOItem stored = new StoredMMOItem();
        stored.type = mmoType;
        stored.itemId = mmoId;
        stored.material = itemStack.getType();
        stored.amount = itemStack.getAmount();
        
        // Store item meta
        ItemMeta meta = itemStack.getItemMeta();
        if (meta != null) {
            if (meta.hasDisplayName()) {
                stored.displayName = meta.getDisplayName();
            }
            if (meta.hasLore()) {
                stored.lore = new ArrayList<>(meta.getLore());
            }
        }
        
        // Store MMOItem specific data
        try {
            // For now, we'll skip the volatile item data extraction
            // This can be implemented later if needed
            stored.storedData.put("mmo_type", mmoType);
            stored.storedData.put("mmo_id", mmoId);
        } catch (Exception e) {
            // Fallback to basic storage
            Logger.getLogger("SoulShopkeeper").warning("Could not extract MMOItem data, using basic storage: " + e.getMessage());
        }
        
        // Store raw NBT if possible (for maximum preservation)
        try {
            stored.rawNBT = extractRawNBT(itemStack);
        } catch (Exception e) {
            // NBT extraction failed, continue without it
        }
        
        return stored;
    }

    /**
     * Creates a StoredMMOItem from database data
     * @param type MMOItem type
     * @param itemId MMOItem ID
     * @param material Bukkit material
     * @param amount Item amount
     * @param displayName Display name
     * @param lore Item lore
     * @param storedData Additional stored data
     * @param rawNBT Raw NBT data
     * @param timestamp Creation timestamp
     * @return StoredMMOItem instance
     */
    public static StoredMMOItem fromDatabaseData(String type, String itemId, Material material,
                                               int amount, String displayName, List<String> lore,
                                               Map<String, Object> storedData, String rawNBT, long timestamp) {
        return new StoredMMOItem(type, itemId, material, amount, displayName, lore, storedData, rawNBT, timestamp);
    }
    

    
    /**
     * Extracts raw NBT data from ItemStack
     */
    private static String extractRawNBT(ItemStack itemStack) {
        // This method would extract raw NBT data
        // Implementation depends on your server version and NBT library
        // For now, return null as placeholder
        return null;
    }
    
    /**
     * Saves this StoredMMOItem to a configuration section
     */
    public void saveToConfig(ConfigurationSection section) {
        section.set("type", type);
        section.set("itemId", itemId);
        section.set("material", material.name());
        section.set("amount", amount);
        section.set("timestamp", timestamp);
        
        if (displayName != null) {
            section.set("displayName", displayName);
        }
        
        if (lore != null && !lore.isEmpty()) {
            section.set("lore", lore);
        }
        
        if (!storedData.isEmpty()) {
            ConfigurationSection dataSection = section.createSection("storedData");
            for (Map.Entry<String, Object> entry : storedData.entrySet()) {
                try {
                    dataSection.set(entry.getKey(), entry.getValue());
                } catch (Exception e) {
                    // Skip non-serializable data
                }
            }
        }
        
        if (rawNBT != null) {
            section.set("rawNBT", rawNBT);
        }
    }
    
    /**
     * Loads a StoredMMOItem from a configuration section
     */
    public static StoredMMOItem loadFromConfig(ConfigurationSection section) {
        if (section == null) {
            return null;
        }
        
        try {
            StoredMMOItem stored = new StoredMMOItem();
            
            stored.type = section.getString("type");
            stored.itemId = section.getString("itemId");
            stored.amount = section.getInt("amount", 1);
            stored.timestamp = section.getLong("timestamp", System.currentTimeMillis());
            
            // Load material
            String materialName = section.getString("material");
            if (materialName != null) {
                try {
                    stored.material = Material.valueOf(materialName);
                } catch (IllegalArgumentException e) {
                    stored.material = Material.STONE; // Fallback
                }
            }
            
            // Load optional data
            stored.displayName = section.getString("displayName");
            stored.lore = section.getStringList("lore");
            stored.rawNBT = section.getString("rawNBT");
            
            // Load stored data
            ConfigurationSection dataSection = section.getConfigurationSection("storedData");
            if (dataSection != null) {
                for (String key : dataSection.getKeys(false)) {
                    stored.storedData.put(key, dataSection.get(key));
                }
            }
            
            return stored;
            
        } catch (Exception e) {
            Logger.getLogger("SoulShopkeeper").severe("Failed to load StoredMMOItem: " + e.getMessage());
            return null;
        }
    }
    
    // Getters
    public String getType() {
        return type;
    }
    
    public String getItemId() {
        return itemId;
    }
    
    public Material getMaterial() {
        return material;
    }
    
    public int getAmount() {
        return amount;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public List<String> getLore() {
        return lore != null ? new ArrayList<>(lore) : new ArrayList<>();
    }
    
    public Map<String, Object> getStoredData() {
        return new HashMap<>(storedData);
    }
    
    public String getRawNBT() {
        return rawNBT;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    // Utility methods
    public boolean hasStoredData() {
        return !storedData.isEmpty();
    }
    
    public boolean hasRawNBT() {
        return rawNBT != null && !rawNBT.isEmpty();
    }
    
    public boolean hasDisplayName() {
        return displayName != null && !displayName.isEmpty();
    }
    
    public boolean hasLore() {
        return lore != null && !lore.isEmpty();
    }
    
    /**
     * Gets a string representation of this stored item
     */
    @Override
    public String toString() {
        return String.format("StoredMMOItem{type='%s', itemId='%s', material=%s, amount=%d}", 
                           type, itemId, material, amount);
    }
    
    /**
     * Checks if this stored item is valid
     */
    public boolean isValid() {
        return type != null && !type.isEmpty() && 
               itemId != null && !itemId.isEmpty() && 
               material != null && amount > 0;
    }
    
    /**
     * Gets the full MMOItem identifier (type.id)
     */
    public String getFullId() {
        return type + "." + itemId;
    }
}
