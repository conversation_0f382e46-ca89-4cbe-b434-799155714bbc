package shyrcs.shopkeeper.gui;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

import java.util.*;

/**
 * Manages GUI interfaces for shopkeeper creation and editing
 */
public class ShopkeeperGUIManager {
    
    private final SoulShopkeeperPlugin plugin;
    
    // GUI sessions to track what players are doing
    private final Map<UUID, GUISession> activeSessions;
    
    public ShopkeeperGUIManager(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
        this.activeSessions = new HashMap<>();
    }
    
    /**
     * Opens the shopkeeper creation GUI
     */
    public void openCreationGUI(Player player) {
        String title = MessageUtils.colorize("&6&lCreate Shopkeeper");
        int size = 54;

        Inventory gui = Bukkit.createInventory(player, size, title);

        // Setup creation GUI layout
        setupCreationGUI(gui);

        // Create session
        GUISession session = new GUISession(player.getUniqueId(), GUIType.CREATION, null);
        activeSessions.put(player.getUniqueId(), session);

        player.openInventory(gui);
    }
    
    /**
     * Opens the shopkeeper editing GUI
     */
    public void openEditGUI(Player player, SoulShopkeeper shopkeeper) {
        String title = MessageUtils.colorize("&6&lEdit: " + shopkeeper.getName());
        int size = 54;

        Inventory gui = Bukkit.createInventory(player, size, title);

        // Setup edit GUI layout
        setupEditGUI(gui, shopkeeper);

        // Create session
        GUISession session = new GUISession(player.getUniqueId(), GUIType.EDIT, shopkeeper);
        activeSessions.put(player.getUniqueId(), session);

        player.openInventory(gui);
    }
    
    /**
     * Opens the trade editing GUI
     */
    public void openTradeEditGUI(Player player, SoulShopkeeper shopkeeper, int tradeIndex) {
        String title = MessageUtils.colorize("&6&lTrade Editor #" + (tradeIndex + 1));
        Inventory gui = Bukkit.createInventory(player, 27, title);

        // Setup trade edit GUI
        setupTradeEditGUI(gui, shopkeeper, tradeIndex);

        // Create session
        GUISession session = new GUISession(player.getUniqueId(), GUIType.TRADE_EDIT, shopkeeper);
        session.setTradeIndex(tradeIndex);
        activeSessions.put(player.getUniqueId(), session);

        plugin.getLogger().info("Created TRADE_EDIT session for player: " + player.getName() +
                               ", tradeIndex: " + tradeIndex + ", shopkeeper: " + shopkeeper.getName());
        plugin.getLogger().info("Total active sessions: " + activeSessions.size());

        player.openInventory(gui);
    }
    
    /**
     * Sets up the creation GUI layout
     */
    private void setupCreationGUI(Inventory gui) {
        // Fill borders with glass panes
        fillBorders(gui, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
        
        // Add creation options
        gui.setItem(13, createItem(Material.VILLAGER_SPAWN_EGG, "&6Create Villager Shopkeeper", 
                                  "&7Click to create a villager shopkeeper"));
        
        gui.setItem(21, createItem(Material.ZOMBIE_SPAWN_EGG, "&6Create Zombie Shopkeeper", 
                                  "&7Click to create a zombie shopkeeper"));
        
        gui.setItem(23, createItem(Material.SKELETON_SPAWN_EGG, "&6Create Skeleton Shopkeeper", 
                                  "&7Click to create a skeleton shopkeeper"));
        
        // Add cancel button
        gui.setItem(49, createItem(Material.BARRIER, "&cCancel", "&7Click to cancel creation"));
    }
    
    /**
     * Sets up the edit GUI layout
     */
    private void setupEditGUI(Inventory gui, SoulShopkeeper shopkeeper) {
        // Fill borders with glass panes
        fillBorders(gui, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
        
        // Shopkeeper info
        gui.setItem(4, createItem(Material.NAME_TAG, "&6Shopkeeper: " + shopkeeper.getName(),
                                 "&7Type: " + shopkeeper.getType(),
                                 "&7Trades: " + shopkeeper.getTradeCount(),
                                 "&7Active: " + (shopkeeper.isActive() ? "&aYes" : "&cNo")));
        
        // Trade slots - dynamic based on max trades setting
        int tradeStartSlot = 10;
        List<ShopkeeperTrade> trades = shopkeeper.getTrades();
        int maxTrades = shopkeeper.getMaxTrades();

        // Calculate how many slots to show (max 45 for GUI size limit)
        int slotsToShow = maxTrades == -1 ? 45 : Math.min(maxTrades, 45);

        for (int i = 0; i < slotsToShow; i++) {
            // Calculate slot position (skip border slots)
            int row = i / 7;
            int col = i % 7;
            int slot = tradeStartSlot + col + (row * 9);

            // Skip if slot is outside GUI bounds
            if (slot >= gui.getSize() - 9) break; // Leave space for control buttons

            if (i < trades.size()) {
                // Existing trade
                ShopkeeperTrade trade = trades.get(i);
                ItemStack tradeItem = createTradeDisplayItem(trade, i);
                gui.setItem(slot, tradeItem);
            } else if (shopkeeper.canAddMoreTrades()) {
                // Empty trade slot
                gui.setItem(slot, createItem(Material.LIME_STAINED_GLASS_PANE, "&aAdd Trade",
                                           "&7Click to add a new trade"));
            } else {
                // Disabled slot
                String maxText = maxTrades == -1 ? "Unlimited" : String.valueOf(maxTrades);
                gui.setItem(slot, createItem(Material.RED_STAINED_GLASS_PANE, "&cMax Trades Reached",
                                           "&7Maximum: " + maxText));
            }
        }
        
        // Control buttons
        gui.setItem(45, createItem(Material.EMERALD, "&aToggle Active", 
                                  "&7Current: " + (shopkeeper.isActive() ? "&aActive" : "&cInactive"),
                                  "&7Click to toggle"));
        
        gui.setItem(49, createItem(Material.BARRIER, "&cClose", "&7Click to close editor"));
        
        gui.setItem(53, createItem(Material.REDSTONE, "&cDelete Shopkeeper",
                                  "&7Shift-click to delete this shopkeeper",
                                  "&c&lWARNING: This cannot be undone!"));
    }
    
    /**
     * Sets up the trade edit GUI layout
     */
    private void setupTradeEditGUI(Inventory gui, SoulShopkeeper shopkeeper, int tradeIndex) {
        // Clear GUI first
        gui.clear();

        // Fill borders with glass panes
        for (int i = 0; i < 9; i++) {
            gui.setItem(i, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
            gui.setItem(i + 18, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
        }
        gui.setItem(9, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
        gui.setItem(17, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));

        ShopkeeperTrade trade = null;
        if (tradeIndex >= 0 && tradeIndex < shopkeeper.getTrades().size()) {
            trade = shopkeeper.getTrades().get(tradeIndex);
        }

        // Trade setup slots - clear them first
        gui.setItem(10, null);
        gui.setItem(12, null);
        gui.setItem(16, null);

        // Load existing trade items if editing
        if (trade != null) {
            ItemStack ingredient1 = trade.getIngredient1Item();
            if (ingredient1 != null) {
                gui.setItem(10, ingredient1.clone());
            }

            ItemStack ingredient2 = trade.getIngredient2Item();
            if (ingredient2 != null) {
                gui.setItem(12, ingredient2.clone());
            }

            ItemStack result = trade.getResultItem();
            if (result != null) {
                gui.setItem(16, result.clone());
            }
        }

        // Add instruction items around the trade slots
        gui.setItem(9, createItem(Material.YELLOW_STAINED_GLASS_PANE, "&eIngredient 1",
                                 "&7Drag items to slot 10 →"));
        gui.setItem(11, createItem(Material.YELLOW_STAINED_GLASS_PANE, "&eIngredient 2",
                                  "&7Drag items to slot 12 →"));
        gui.setItem(15, createItem(Material.GREEN_STAINED_GLASS_PANE, "&aResult",
                                  "&7Drag items to slot 16 →"));

        // Control buttons
        gui.setItem(22, createItem(Material.EMERALD, "&aSave Trade", "&7Click to save this trade"));
        gui.setItem(24, createItem(Material.BARRIER, "&cBack", "&7Click to go back"));

        if (trade != null) {
            gui.setItem(26, createItem(Material.REDSTONE, "&cDelete Trade", "&7Click to delete this trade"));
        }
    }
    
    /**
     * Creates a display item for a trade
     */
    private ItemStack createTradeDisplayItem(ShopkeeperTrade trade, int index) {
        Material material = Material.PAPER;
        String name = "&6Trade #" + (index + 1);
        List<String> lore = new ArrayList<>();
        
        // Get trade items for display
        ItemStack result = trade.getResultItem();
        ItemStack ingredient1 = trade.getIngredient1Item();
        ItemStack ingredient2 = trade.getIngredient2Item();
        
        if (result != null) {
            lore.add("&7Result: &f" + getItemDisplayName(result));
        }
        
        if (ingredient1 != null) {
            lore.add("&7Ingredient 1: &f" + getItemDisplayName(ingredient1));
        }
        
        if (ingredient2 != null) {
            lore.add("&7Ingredient 2: &f" + getItemDisplayName(ingredient2));
        }
        
        lore.add("");
        lore.add("&7Status: " + (trade.isEnabled() ? "&aEnabled" : "&cDisabled"));
        
        if (trade.getMaxUses() != -1) {
            lore.add("&7Uses: &f" + trade.getUses() + "/" + trade.getMaxUses());
        } else {
            lore.add("&7Uses: &fUnlimited");
        }
        
        lore.add("");
        lore.add("&eLeft-click to edit");
        lore.add("&eRight-click to toggle enabled");
        
        return createItem(material, name, lore.toArray(new String[0]));
    }
    
    /**
     * Gets display name for an item
     */
    private String getItemDisplayName(ItemStack item) {
        if (item == null) return "None";
        
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return MessageUtils.stripColors(item.getItemMeta().getDisplayName());
        }
        
        // Check if it's an MMOItem
        String mmoInfo = plugin.getMMOItemStorage().getMMOItemInfo(item);
        if (mmoInfo != null) {
            return mmoInfo;
        }
        
        return item.getType().name().toLowerCase().replace('_', ' ');
    }
    
    /**
     * Creates an ItemStack with name and lore
     */
    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(MessageUtils.colorize(name));
            
            if (lore.length > 0) {
                List<String> coloredLore = new ArrayList<>();
                for (String line : lore) {
                    coloredLore.add(MessageUtils.colorize(line));
                }
                meta.setLore(coloredLore);
            }
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Fills the borders of a GUI with the specified item
     */
    private void fillBorders(Inventory gui, ItemStack item) {
        int size = gui.getSize();
        int rows = size / 9;
        
        // Top and bottom rows
        for (int i = 0; i < 9; i++) {
            gui.setItem(i, item);
            gui.setItem(size - 9 + i, item);
        }
        
        // Left and right columns
        for (int i = 1; i < rows - 1; i++) {
            gui.setItem(i * 9, item);
            gui.setItem(i * 9 + 8, item);
        }
    }
    
    // Session management
    public GUISession getSession(UUID playerId) {
        GUISession session = activeSessions.get(playerId);
        plugin.getLogger().info("Getting session for player: " + playerId +
                               ", found: " + (session != null ? session.getType() : "null") +
                               ", total sessions: " + activeSessions.size());
        return session;
    }
    
    public void removeSession(UUID playerId) {
        GUISession removed = activeSessions.remove(playerId);
        plugin.getLogger().info("Removed session for player: " + playerId +
                               ", was: " + (removed != null ? removed.getType() : "null") +
                               ", remaining sessions: " + activeSessions.size());
    }
    
    public boolean hasSession(UUID playerId) {
        return activeSessions.containsKey(playerId);
    }
    
    // GUI Session class
    public static class GUISession {
        private final UUID playerId;
        private final GUIType type;
        private final SoulShopkeeper shopkeeper;
        private int tradeIndex = -1;
        
        public GUISession(UUID playerId, GUIType type, SoulShopkeeper shopkeeper) {
            this.playerId = playerId;
            this.type = type;
            this.shopkeeper = shopkeeper;
        }
        
        public UUID getPlayerId() { return playerId; }
        public GUIType getType() { return type; }
        public SoulShopkeeper getShopkeeper() { return shopkeeper; }
        public int getTradeIndex() { return tradeIndex; }
        public void setTradeIndex(int tradeIndex) { this.tradeIndex = tradeIndex; }
    }
    
    // GUI Types
    public enum GUIType {
        CREATION,
        EDIT,
        TRADE_EDIT
    }
}
