package shyrcs.Skills;

import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.bukkit.inventory.ItemStack;

import java.util.Collection;

/**
 * Custom event được gọi khi Farming Zone harvest cây trồng
 */
public class FarmingZoneEvent extends Event {
    private static final HandlerList handlers = new HandlerList();
    
    private final Player player;
    private final Block cropBlock;
    private final Collection<ItemStack> drops;
    private final boolean addedToStorage;
    private final int totalItemsHarvested;
    private final String cropType;
    
    public FarmingZoneEvent(Player player, Block cropBlock, Collection<ItemStack> drops, 
                           boolean addedToStorage, int totalItemsHarvested, String cropType) {
        this.player = player;
        this.cropBlock = cropBlock;
        this.drops = drops;
        this.addedToStorage = addedToStorage;
        this.totalItemsHarvested = totalItemsHarvested;
        this.cropType = cropType;
    }
    
    /**
     * Lấy player đã kích hoạt farming zone
     */
    public Player getPlayer() {
        return player;
    }
    
    /**
     * Lấy block cây trồng được harvest
     */
    public Block getCropBlock() {
        return cropBlock;
    }
    
    /**
     * Lấy danh sách items được drop
     */
    public Collection<ItemStack> getDrops() {
        return drops;
    }
    
    /**
     * Kiểm tra items có được thêm vào storage không
     */
    public boolean isAddedToStorage() {
        return addedToStorage;
    }
    
    /**
     * Lấy tổng số items được harvest
     */
    public int getTotalItemsHarvested() {
        return totalItemsHarvested;
    }
    
    /**
     * Lấy loại cây trồng
     */
    public String getCropType() {
        return cropType;
    }
    
    @Override
    public HandlerList getHandlers() {
        return handlers;
    }
    
    public static HandlerList getHandlerList() {
        return handlers;
    }
}
