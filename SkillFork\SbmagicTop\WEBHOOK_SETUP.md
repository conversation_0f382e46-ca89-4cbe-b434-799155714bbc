# SbmagicTop - Webhook Auto-Update Setup Guide

## Tính năng mới
Plugin SbmagicTop đã được cập nhật với tính năng webhook auto-update:
- Tự động cập nhật leaderboard mỗi 1 tiếng (có thể tùy chỉnh)
- Cậ<PERSON> nhật khi server khởi động
- Cập nhật khi reload plugin
- Tự động tạo message mới hoặc edit message cũ
- Lưu trữ message IDs để có thể edit sau này

## Cấu hình

### 1. C<PERSON>u hình Discord Bot
Trong file `config.yml`:

```yaml
discord:
  token: "YOUR_BOT_TOKEN_HERE"
  guild-id: "YOUR_GUILD_ID_HERE"
  
  # Webhook settings for auto-update
  webhook:
    # Webhook URL for sending leaderboard updates (lấy từ Discord channel settings)
    url: "YOUR_WEBHOOK_URL_HERE"
    # Enable/disable webhook functionality
    enabled: true
    # Username for webhook messages
    username: "SkyBlock Leaderboard"
    # Avatar URL for webhook messages
    avatar-url: ""

settings:
  # Auto-update settings
  auto-update:
    # Enable auto-update feature
    enabled: true
    # Update interval in minutes (default: 60 minutes = 1 hour)
    interval: 60
    # Update on server startup
    on-startup: true
    # Update on plugin reload
    on-reload: true
```

### 2. Cấu hình từng loại Top
Trong các file `top/*.yml` (ví dụ: `balance.yml`, `donate.yml`):

```yaml
name: 💰 Top Balance
description: Bảng Xếp Hạng Balance
color: 0xFFD700
thumbnail: ''

# Webhook settings for this specific top
webhook:
  enabled: true
  url: ''  # Leave empty to use global webhook URL
  update-interval: 60  # Minutes

content:
  - "🥇 %ajlb_lb_vault_eco_balance_1_alltime_name% - %ajlb_lb_vault_eco_balance_1_alltime_value%"
  # ... more content
```

## Lệnh quản lý

### Lệnh cơ bản
- `/sbmagictop reload` - Reload toàn bộ config và restart auto-update
- `/sbmagictop status` - Xem trạng thái plugin và webhook
- `/sbmagictop update` - Trigger cập nhật webhook ngay lập tức

### Lệnh webhook
- `/sbmagictop webhook status` - Xem trạng thái webhook chi tiết
- `/sbmagictop webhook update` - Cập nhật webhook thủ công
- `/sbmagictop webhook clear` - Xóa tất cả message IDs (sẽ tạo message mới)

## Cách lấy Webhook URL

1. Vào Discord server mà bạn muốn gửi leaderboard
2. Vào channel settings → Integrations → Webhooks
3. Tạo webhook mới hoặc sử dụng webhook có sẵn
4. Copy webhook URL và paste vào config.yml

## Cách hoạt động

1. **Khởi động**: Plugin sẽ tự động gửi message leaderboard qua webhook
2. **Cập nhật định kỳ**: Mỗi 1 tiếng (hoặc theo cấu hình), plugin sẽ edit message cũ với dữ liệu mới
3. **Lưu trữ**: Message IDs được lưu trong file `webhook-data.yml` để có thể edit sau này
4. **Fallback**: Nếu không thể edit message cũ (bị xóa, lỗi), plugin sẽ tự động tạo message mới
5. **Độc lập**: Webhook hoạt động độc lập, không cần bot Discord trong server đích

## Troubleshooting

### Webhook không hoạt động
1. Kiểm tra `discord.webhook.enabled: true`
2. Kiểm tra `discord.webhook.url` có đúng không
3. Kiểm tra webhook URL có hết hạn không
4. Xem log để kiểm tra lỗi HTTP

### Message không được edit
1. Kiểm tra message có bị xóa không
2. Sử dụng `/sbmagictop webhook clear` để reset message IDs
3. Plugin sẽ tự động tạo message mới nếu không thể edit

### Auto-update không chạy
1. Kiểm tra `settings.auto-update.enabled: true`
2. Kiểm tra `discord.webhook.enabled: true`
3. Kiểm tra interval có >= 5 phút không
4. Sử dụng `/sbmagictop status` để xem trạng thái

## File cấu hình được tạo

- `webhook-data.yml`: Lưu trữ message IDs của các webhook
- Cấu trúc:
```yaml
message-ids:
  balance: "1234567890123456789"
  donate: "9876543210987654321"
```

## Lưu ý quan trọng

1. **Webhook URL**: Plugin sử dụng HTTP requests trực tiếp đến Discord webhook, không cần bot trong server đích
2. **Rate Limiting**: Plugin có delay 1 giây giữa các update để tránh rate limit Discord
3. **Permissions**: Webhook tự động có quyền gửi message, không cần cấu hình thêm
4. **Backup**: Message IDs được lưu tự động, không cần backup thủ công
5. **Cross-server**: Có thể gửi từ server A sang server B mà không cần bot ở server B

## Ví dụ cấu hình hoàn chỉnh

```yaml
discord:
  token: "MTA5MTYyNzA2OTUxNzA4Njc4MQ.GeQ5v8.sMosiTrRUtid4ot79lEaWG4JcY8iznrRrb8X2I"
  guild-id: "1395472849107423452"

  webhook:
    url: "https://discord.com/api/webhooks/1234567890/abcdefghijklmnop"  # Webhook URL từ Discord
    enabled: true
    username: "SkyBlock Leaderboard"
    avatar-url: ""

settings:
  auto-update:
    enabled: true
    interval: 60  # 1 tiếng
    on-startup: true
    on-reload: true
```
