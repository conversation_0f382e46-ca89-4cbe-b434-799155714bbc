package shyrcs.shopkeeper.storage.database;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * SQLite implementation for shopkeeper data management
 * Provides better performance and reliability than YAML storage
 */
public class SQLiteShopkeeperDataManager {
    
    private final SoulShopkeeperPlugin plugin;
    private final DatabaseManager databaseManager;
    private final Logger logger;
    
    // Cache for shopkeepers
    private final Map<UUID, SoulShopkeeper> shopkeepers;
    private final Map<Location, UUID> locationIndex;
    private final Map<UUID, Set<UUID>> playerShopkeepers;
    
    // SQL statements
    private static final String INSERT_SHOPKEEPER = 
        "INSERT OR REPLACE INTO %sshopkeepers (id, name, type, world, x, y, z, yaw, pitch, owner_id, active, created_time, last_modified) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    private static final String SELECT_SHOPKEEPER = 
        "SELECT * FROM %sshopkeepers WHERE id = ?";
    
    private static final String SELECT_ALL_SHOPKEEPERS = 
        "SELECT * FROM %sshopkeepers";
    
    private static final String DELETE_SHOPKEEPER = 
        "DELETE FROM %sshopkeepers WHERE id = ?";
    
    private static final String INSERT_TRADE = 
        "INSERT OR REPLACE INTO %strades (shopkeeper_id, trade_index, result_item_id, ingredient1_item_id, ingredient2_item_id, max_uses, uses, enabled, created_time, last_used) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    private static final String SELECT_TRADES = 
        "SELECT * FROM %strades WHERE shopkeeper_id = ? ORDER BY trade_index";
    
    private static final String DELETE_TRADES = 
        "DELETE FROM %strades WHERE shopkeeper_id = ?";
    
    public SQLiteShopkeeperDataManager(SoulShopkeeperPlugin plugin, DatabaseManager databaseManager) {
        this.plugin = plugin;
        this.databaseManager = databaseManager;
        this.logger = plugin.getLogger();
        this.shopkeepers = new ConcurrentHashMap<>();
        this.locationIndex = new ConcurrentHashMap<>();
        this.playerShopkeepers = new ConcurrentHashMap<>();
        
        // Load data from database
        loadAllShopkeepers();
        
        // Start auto-save task
        startAutoSaveTask();
    }
    
    /**
     * Loads all shopkeepers from the database
     */
    private void loadAllShopkeepers() {
        try {
            String sql = String.format(SELECT_ALL_SHOPKEEPERS, databaseManager.getTablePrefix());
            
            try (Connection conn = databaseManager.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                int count = 0;
                while (rs.next()) {
                    SoulShopkeeper shopkeeper = createShopkeeperFromResultSet(rs);
                    if (shopkeeper != null) {
                        // Load trades for this shopkeeper
                        loadTradesForShopkeeper(shopkeeper);
                        
                        // Add to cache
                        shopkeepers.put(shopkeeper.getId(), shopkeeper);
                        locationIndex.put(shopkeeper.getLocation(), shopkeeper.getId());
                        
                        if (shopkeeper.getOwnerId() != null) {
                            playerShopkeepers.computeIfAbsent(shopkeeper.getOwnerId(), k -> new HashSet<>())
                                           .add(shopkeeper.getId());
                        }
                        
                        count++;
                    }
                }
                
                logger.info("Loaded " + count + " shopkeepers from database");
            }
            
        } catch (SQLException e) {
            logger.severe("Failed to load shopkeepers from database: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Creates a SoulShopkeeper from database ResultSet
     */
    private SoulShopkeeper createShopkeeperFromResultSet(ResultSet rs) throws SQLException {
        try {
            UUID id = UUID.fromString(rs.getString("id"));
            String name = rs.getString("name");
            String type = rs.getString("type");
            
            // Create location
            String worldName = rs.getString("world");
            World world = Bukkit.getWorld(worldName);
            if (world == null) {
                logger.warning("World not found for shopkeeper " + id + ": " + worldName);
                return null;
            }
            
            double x = rs.getDouble("x");
            double y = rs.getDouble("y");
            double z = rs.getDouble("z");
            float yaw = rs.getFloat("yaw");
            float pitch = rs.getFloat("pitch");
            Location location = new Location(world, x, y, z, yaw, pitch);
            
            // Get owner
            String ownerIdString = rs.getString("owner_id");
            UUID ownerId = null;
            if (ownerIdString != null && !ownerIdString.isEmpty()) {
                try {
                    ownerId = UUID.fromString(ownerIdString);
                } catch (IllegalArgumentException e) {
                    // Invalid UUID, continue without owner
                }
            }
            
            // Create shopkeeper
            SoulShopkeeper shopkeeper = new SoulShopkeeper(id, name, type, location, ownerId);
            
            // Set additional properties
            boolean active = rs.getBoolean("active");
            shopkeeper.setActive(active);
            
            return shopkeeper;
            
        } catch (Exception e) {
            logger.warning("Failed to create shopkeeper from ResultSet: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Loads trades for a specific shopkeeper
     */
    private void loadTradesForShopkeeper(SoulShopkeeper shopkeeper) throws SQLException {
        String sql = String.format(SELECT_TRADES, databaseManager.getTablePrefix());
        
        try (Connection conn = databaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, shopkeeper.getId().toString());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    ShopkeeperTrade trade = createTradeFromResultSet(rs);
                    if (trade != null) {
                        shopkeeper.addTrade(trade);
                    }
                }
            }
        }
    }
    
    /**
     * Creates a ShopkeeperTrade from database ResultSet
     */
    private ShopkeeperTrade createTradeFromResultSet(ResultSet rs) throws SQLException {
        try {
            String resultItemId = rs.getString("result_item_id");
            String ingredient1Id = rs.getString("ingredient1_item_id");
            String ingredient2Id = rs.getString("ingredient2_item_id");
            int maxUses = rs.getInt("max_uses");
            int uses = rs.getInt("uses");
            boolean enabled = rs.getBoolean("enabled");
            
            return new ShopkeeperTrade(resultItemId, ingredient1Id, ingredient2Id, maxUses, uses, enabled);
            
        } catch (Exception e) {
            logger.warning("Failed to create trade from ResultSet: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Saves a shopkeeper to the database
     */
    public boolean saveShopkeeper(SoulShopkeeper shopkeeper) {
        try {
            // Save shopkeeper data
            String sql = String.format(INSERT_SHOPKEEPER, databaseManager.getTablePrefix());
            
            try (Connection conn = databaseManager.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setString(1, shopkeeper.getId().toString());
                stmt.setString(2, shopkeeper.getName());
                stmt.setString(3, shopkeeper.getType());
                stmt.setString(4, shopkeeper.getLocation().getWorld().getName());
                stmt.setDouble(5, shopkeeper.getLocation().getX());
                stmt.setDouble(6, shopkeeper.getLocation().getY());
                stmt.setDouble(7, shopkeeper.getLocation().getZ());
                stmt.setFloat(8, shopkeeper.getLocation().getYaw());
                stmt.setFloat(9, shopkeeper.getLocation().getPitch());
                stmt.setString(10, shopkeeper.getOwnerId() != null ? shopkeeper.getOwnerId().toString() : null);
                stmt.setBoolean(11, shopkeeper.isActive());
                stmt.setLong(12, shopkeeper.getCreatedTime());
                stmt.setLong(13, shopkeeper.getLastModified());
                
                stmt.executeUpdate();
            }
            
            // Save trades
            saveTradesForShopkeeper(shopkeeper);
            
            if (plugin.getConfig().getBoolean("general.debug", false)) {
                logger.info("Saved shopkeeper to database: " + shopkeeper.getId());
            }
            
            return true;
            
        } catch (SQLException e) {
            logger.severe("Failed to save shopkeeper to database: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Saves trades for a specific shopkeeper
     */
    private void saveTradesForShopkeeper(SoulShopkeeper shopkeeper) throws SQLException {
        // First, delete existing trades
        String deleteSql = String.format(DELETE_TRADES, databaseManager.getTablePrefix());
        try (Connection conn = databaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(deleteSql)) {
            
            stmt.setString(1, shopkeeper.getId().toString());
            stmt.executeUpdate();
        }
        
        // Then, insert new trades
        String insertSql = String.format(INSERT_TRADE, databaseManager.getTablePrefix());
        try (Connection conn = databaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(insertSql)) {
            
            List<ShopkeeperTrade> trades = shopkeeper.getTrades();
            for (int i = 0; i < trades.size(); i++) {
                ShopkeeperTrade trade = trades.get(i);
                
                stmt.setString(1, shopkeeper.getId().toString());
                stmt.setInt(2, i);
                stmt.setString(3, trade.getResultItemId());
                stmt.setString(4, trade.getIngredient1Id());
                stmt.setString(5, trade.getIngredient2Id());
                stmt.setInt(6, trade.getMaxUses());
                stmt.setInt(7, trade.getUses());
                stmt.setBoolean(8, trade.isEnabled());
                stmt.setLong(9, trade.getCreatedTime());
                stmt.setLong(10, trade.getLastUsed());
                
                stmt.addBatch();
            }
            
            stmt.executeBatch();
        }
    }
    
    /**
     * Deletes a shopkeeper from the database
     */
    public boolean deleteShopkeeper(UUID shopkeeperId) {
        try {
            String sql = String.format(DELETE_SHOPKEEPER, databaseManager.getTablePrefix());
            
            try (Connection conn = databaseManager.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setString(1, shopkeeperId.toString());
                int affected = stmt.executeUpdate();
                
                if (affected > 0) {
                    // Remove from cache
                    SoulShopkeeper shopkeeper = shopkeepers.remove(shopkeeperId);
                    if (shopkeeper != null) {
                        locationIndex.remove(shopkeeper.getLocation());
                        
                        if (shopkeeper.getOwnerId() != null) {
                            Set<UUID> playerShops = playerShopkeepers.get(shopkeeper.getOwnerId());
                            if (playerShops != null) {
                                playerShops.remove(shopkeeperId);
                                if (playerShops.isEmpty()) {
                                    playerShopkeepers.remove(shopkeeper.getOwnerId());
                                }
                            }
                        }
                    }
                    
                    return true;
                }
            }
            
        } catch (SQLException e) {
            logger.severe("Failed to delete shopkeeper from database: " + e.getMessage());
            e.printStackTrace();
        }
        
        return false;
    }
    
    /**
     * Saves all shopkeepers to the database
     */
    public void saveAll() {
        int saved = 0;
        for (SoulShopkeeper shopkeeper : shopkeepers.values()) {
            if (saveShopkeeper(shopkeeper)) {
                saved++;
            }
        }
        
        if (plugin.getConfig().getBoolean("general.debug", false)) {
            logger.info("Saved " + saved + " shopkeepers to database");
        }
    }
    
    /**
     * Starts the auto-save task
     */
    private void startAutoSaveTask() {
        int interval = plugin.getConfig().getInt("general.auto-save-interval", 5) * 60 * 20; // Convert minutes to ticks
        
        Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            saveAll();
        }, interval, interval);
    }
    
    // Public API methods (same as original ShopkeeperDataManager)
    
    public void addShopkeeper(SoulShopkeeper shopkeeper) {
        shopkeepers.put(shopkeeper.getId(), shopkeeper);
        locationIndex.put(shopkeeper.getLocation(), shopkeeper.getId());
        
        if (shopkeeper.getOwnerId() != null) {
            playerShopkeepers.computeIfAbsent(shopkeeper.getOwnerId(), k -> new HashSet<>()).add(shopkeeper.getId());
        }
        
        // Save to database immediately
        saveShopkeeper(shopkeeper);
    }
    
    public void removeShopkeeper(UUID id) {
        deleteShopkeeper(id);
    }
    
    public SoulShopkeeper getShopkeeper(UUID id) {
        return shopkeepers.get(id);
    }
    
    public SoulShopkeeper getShopkeeperAt(Location location) {
        UUID id = locationIndex.get(location);
        return id != null ? shopkeepers.get(id) : null;
    }
    
    public Collection<SoulShopkeeper> getAllShopkeepers() {
        return new ArrayList<>(shopkeepers.values());
    }
    
    public Collection<SoulShopkeeper> getPlayerShopkeepers(UUID playerId) {
        Set<UUID> shopIds = playerShopkeepers.get(playerId);
        if (shopIds == null) {
            return new ArrayList<>();
        }
        
        List<SoulShopkeeper> result = new ArrayList<>();
        for (UUID id : shopIds) {
            SoulShopkeeper shop = shopkeepers.get(id);
            if (shop != null) {
                result.add(shop);
            }
        }
        return result;
    }
    
    public int getPlayerShopkeeperCount(UUID playerId) {
        Set<UUID> shopIds = playerShopkeepers.get(playerId);
        return shopIds != null ? shopIds.size() : 0;
    }

    public SoulShopkeeperPlugin getPlugin() {
        return plugin;
    }
}
