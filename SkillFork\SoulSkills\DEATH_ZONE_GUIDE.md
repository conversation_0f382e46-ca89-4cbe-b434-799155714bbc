# 💀 HƯỚNG DẪN DEATH ZONE SKILL

## 📋 **Tổng Quan**

Death Zone Skill là một custom skill mạnh mẽ hoạt động thông qua NBT tags, tạo ra một mái vòm tử thần có thể:
- Tạo dome particle tím với hiệu ứng lồng giam
- Gây 20 HP damage/giây (bỏ qua giáp)
- <PERSON>ăn không cho người chơi thoát khỏi vòm
- Kéo dài 5 giây với bán kính 15 blocks

**Hoạt động:** Thông qua NBT tags trên item

---

## 🎯 **Cách Sử Dụng**

### **1. Thêm NBT Tag vào Item**

Sử dụng lệnh để thêm NBT tag với format:
```
death_zone <click_type> <cooldown>
```

**Ví dụ lệnh:**
```
/mmoitems modify <type> <id> add-nbt DEATH_ZONE "death_zone left_click 30"
```

### **2. Format Chi Tiết**

| Tham Số | Mô <PERSON> | Ví <PERSON> |
|---------|-------|-------|
| `death_zone` | Keyword bắt buộc | `death_zone` |
| `click_type` | Loại click để kích hoạt | `left_click`, `right_click`, `shift_left_click`, `shift_right_click` |
| `cooldown` | Thời gian cooldown (giây) | `30`, `60`, `120` |

---

## 🌟 **Ví Dụ Thực Tế**

### **⚔️ Death Wand**
```
/mmoitems modify weapon death_wand add-nbt DEATH_ZONE "death_zone right_click 30"
```
- **NBT:** `death_zone right_click 30`
- **Kích hoạt:** Right click
- **Cooldown:** 30 giây

### **🗡️ Cursed Sword**
```
/mmoitems modify weapon cursed_sword add-nbt DEATH_ZONE "death_zone left_click 45"
```
- **NBT:** `death_zone left_click 45`
- **Kích hoạt:** Left click
- **Cooldown:** 45 giây

### **🔮 Dark Orb**
```
/mmoitems modify consumable dark_orb add-nbt DEATH_ZONE "death_zone shift_right_click 60"
```
- **NBT:** `death_zone shift_right_click 60`
- **Kích hoạt:** Shift + Right click
- **Cooldown:** 60 giây

---

## ⚡ **Tính Năng Skill**

### **🏛️ Dome Structure**
- **Bán kính:** 15 blocks
- **Hình dạng:** Nửa hình cầu (dome)
- **Particle:** Dragon Breath (tím) + Witch (lồng giam)
- **Hiệu ứng:** Lồng giam với các đường thẳng đứng và vòng ngang

### **💀 Damage System**
- **Damage:** 20 HP mỗi giây
- **Loại:** True damage (bỏ qua giáp)
- **Target:** Tất cả players trừ caster
- **Hiệu ứng:** Damage indicator particles

### **🚫 Barrier System**
- **Chức năng:** Ngăn players thoát khỏi dome
- **Cơ chế:** Teleport về trong dome nếu cố thoát
- **Hiệu ứng:** Barrier particles khi bị chặn

### **⏱️ Duration & Cooldown**
- **Thời gian:** 5 giây cố định
- **Cooldown:** Tùy chỉnh theo config
- **Hiển thị:** ActionBar cooldown timer

---

## 🎮 **Hướng Dẫn Tạo Item**

### **Bước 1: Tạo Base Item**
```
/mmoitems create weapon death_wand
```

### **Bước 2: Thêm Death Zone NBT**
```
/mmoitems modify weapon death_wand add-nbt DEATH_ZONE "death_zone right_click 30"
```

### **Bước 3: Tùy chỉnh Item (Optional)**
```
/mmoitems modify weapon death_wand set display-name "§5§lDeath Wand"
/mmoitems modify weapon death_wand set material BLAZE_ROD
/mmoitems modify weapon death_wand add-lore "§7Right click để kích hoạt Death Zone"
/mmoitems modify weapon death_wand add-lore "§7Tạo dome tím gây 20HP/s damage"
```

### **Bước 4: Lấy Item**
```
/mmoitems give <player> weapon death_wand
```

---

## 📊 **Thông Số Kỹ Thuật**

### **Performance Optimization**
- Particle spawn: Mỗi 0.2 giây (4 ticks)
- Damage check: Mỗi 1 giây (20 ticks)  
- Escape check: Mỗi 0.25 giây (5 ticks)
- Dome points: 60 điểm (tối ưu lag)

### **Collision Detection**
- Sphere equation: x² + y² + z² ≤ r²
- Radius: 15 blocks
- Center: Player location + Y offset

### **Memory Management**
- Auto cleanup khi skill kết thúc
- Cleanup khi plugin disable
- HashMap tracking active domes

---

## ⚠️ **Lưu Ý Quan Trọng**

1. **Một dome mỗi player** - Không thể có nhiều dome cùng lúc
2. **True damage** - Bỏ qua mọi loại giáp và protection
3. **No friendly fire** - Caster không bị damage
4. **Cooldown display** - Hiển thị trên ActionBar
5. **Performance optimized** - Giảm lag với particle batching

---

## 🐛 **Troubleshooting**

### **Skill không kích hoạt?**
- Kiểm tra click type đúng chưa
- Xem có đang trong cooldown không
- Đảm bảo item có NBT tag DEATH_ZONE với format đúng
- Kiểm tra NBT bằng lệnh: `/mmoitems browse weapon death_wand`

### **Lag khi dùng skill?**
- Particle đã được tối ưu
- Nếu vẫn lag, có thể giảm DOME_POINTS trong code

### **Players thoát được dome?**
- Escape check chạy mỗi 0.25s
- Có thể có delay nhỏ do tick rate

---

## 🎉 **Kết Luận**

Death Zone Skill mang lại:
- **Hiệu ứng visual ấn tượng** với dome particle tím
- **Gameplay mechanics độc đáo** với barrier system
- **Balance tốt** với cooldown và duration cố định
- **Performance tối ưu** cho server

**Format NBT:** `death_zone <click_type> <cooldown>`
**Ví dụ:** `death_zone right_click 30`
**Lệnh:** `/mmoitems modify <type> <id> add-nbt DEATH_ZONE "death_zone right_click 30"`

Chúc bạn sử dụng Death Zone skill hiệu quả! 💀⚡
