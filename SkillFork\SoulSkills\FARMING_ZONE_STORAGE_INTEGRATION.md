# 📦 FARMING ZONE - EXTRASTORAGE INTEGRATION

## 🎯 **Tổng Quan**

Farming Zone skill đã được tích hợp với ExtraStorage plugin để tự động thêm items vào kho của player:

- ✅ **Auto Storage** - Items từ farming tự động vào kho
- ✅ **Smart Filtering** - Tự động thêm item mới vào filter
- ✅ **Fallback System** - Drop items nếu không thể store
- ✅ **Performance Optimized** - Sử dụng reflection để tránh dependency
- ✅ **Error Handling** - Graceful fallback nếu ExtraStorage không có

---

## 🔧 **Cách Hoạt Động**

### **Flow Process:**
1. **Farming Zone harvest cây** → Lấy drops
2. **Check ExtraStorage** → Kiểm tra plugin có tồn tại không
3. **Get User Storage** → Lấy storage của player
4. **Check Filter** → Kiểm tra item c<PERSON> đ<PERSON> filter không
5. **Auto Add Filter** → Thêm item mới vào filter nếu cần
6. **Add to Storage** → Thêm items vào kho
7. **Fallback Drop** → Drop nếu không thể store

### **Technical Implementation:**
```java
// Reflection-based integration
Class<?> storageAPIClass = Class.forName("me.hsgamer.extrastorage.api.StorageAPI");
Object storageAPI = storageAPIClass.getMethod("getInstance").invoke(null);

// Get user storage
Object user = storageAPIClass.getMethod("getUser", UUID.class)
    .invoke(storageAPI, player.getUniqueId());
Object storage = userClass.getMethod("getStorage").invoke(user);

// Check and add to storage
boolean canStore = (Boolean) canStoreMethod.invoke(storage, item);
if (canStore) {
    addMethod.invoke(storage, item, item.getAmount());
}
```

---

## 🌾 **Supported Items**

### **Crop Items được Auto Store:**
- ✅ **Wheat** - Lúa mì
- ✅ **Wheat Seeds** - Hạt giống lúa mì
- ✅ **Carrots** - Cà rót
- ✅ **Potatoes** - Khoai tây
- ✅ **Beetroots** - Củ cải đường
- ✅ **Beetroot Seeds** - Hạt giống củ cải
- ✅ **Nether Wart** - Nether Wart
- ✅ **Cocoa Beans** - Hạt cacao
- ✅ **Sweet Berries** - Berry ngọt

### **Auto Filter Logic:**
```java
// Nếu item chưa được filter
if (!canStore) {
    // Tự động thêm vào filter
    storage.addNewItem(item);
    
    // Check lại
    canStore = storage.canStore(item);
}
```

---

## 📊 **Storage Benefits**

### **Trước khi có Integration:**
- ❌ Items drop trên đất
- ❌ Cần pickup manual
- ❌ Có thể bị mất items
- ❌ Lag do nhiều item entities

### **Sau khi có Integration:**
- ✅ Items tự động vào kho
- ✅ Không cần pickup
- ✅ Không bị mất items
- ✅ Không lag do item entities
- ✅ Organized storage

---

## 🎮 **Cách Sử Dụng**

### **Setup ExtraStorage:**
1. **Cài đặt ExtraStorage plugin**
2. **Tạo storage cho player:** `/storage create`
3. **Setup filter** (optional - sẽ auto add)
4. **Enable storage:** `/storage toggle`

### **Test Farming Zone:**
```bash
# Tạo farming item
/mmoitems create weapon storage_farm_test
/mmoitems modify weapon storage_farm_test add-nbt FARMING_ZONE "farming_zone 30 right_click 60"
/mmoitems give <player> weapon storage_farm_test

# Test:
# 1. Đứng gần cây trồng mature
# 2. Right click để kích hoạt
# 3. Quan sát items tự động vào storage
# 4. Check storage: /storage gui
```

### **Verify Integration:**
```bash
# Check storage trước farming
/storage gui

# Kích hoạt farming zone
# Right click farming item

# Check storage sau farming
/storage gui
# → Sẽ thấy items mới được thêm vào
```

---

## 🔍 **Debug & Troubleshooting**

### **Items không vào storage?**

**Possible Causes:**
1. **ExtraStorage plugin không có**
   - Solution: Cài đặt ExtraStorage plugin

2. **Player chưa có storage**
   - Solution: `/storage create`

3. **Storage bị disable**
   - Solution: `/storage toggle` để enable

4. **Storage đã full**
   - Solution: Expand storage hoặc clear items

5. **Item không được filter**
   - Solution: Auto filter sẽ handle, hoặc manual add

### **Debug Commands:**
```bash
# Check storage status
/storage info

# Check storage space
/storage gui

# Check filter
/storage filter

# Reset storage (careful!)
/storage reset
```

---

## ⚡ **Performance Impact**

### **Reflection Overhead:**
- **Minimal impact** - Chỉ call khi harvest
- **Cached methods** - Reflection objects được cache
- **Error handling** - Graceful fallback nếu fail

### **Storage Operations:**
- **Batch processing** - Multiple items cùng lúc
- **Async safe** - Không block main thread
- **Memory efficient** - Không store unnecessary data

### **Comparison:**
```
Without Storage Integration:
- 100 items harvested = 100 item entities spawned
- Potential lag from item entities
- Manual pickup required

With Storage Integration:
- 100 items harvested = 0 item entities spawned
- No lag from item entities
- Automatic storage
```

---

## 🎯 **Best Practices**

### **For Server Admins:**
1. **Install ExtraStorage** trước khi enable farming zones
2. **Configure storage limits** phù hợp với server economy
3. **Monitor storage usage** để tránh abuse
4. **Backup storage data** regularly

### **For Players:**
1. **Setup storage** trước khi farming
2. **Enable storage** khi sử dụng farming zones
3. **Check storage space** định kỳ
4. **Organize filters** cho efficiency

### **For Developers:**
1. **Test integration** với different ExtraStorage versions
2. **Handle edge cases** gracefully
3. **Monitor performance** impact
4. **Update reflection** nếu API changes

---

## 📈 **Statistics & Events**

### **FarmingZoneEvent:**
```java
// Custom event được fire mỗi khi harvest
public class FarmingZoneEvent extends Event {
    private final Player player;
    private final Block cropBlock;
    private final Collection<ItemStack> drops;
    private final boolean addedToStorage;
    private final int totalItemsHarvested;
    private final String cropType;
}
```

### **Event Usage:**
```java
@EventHandler
public void onFarmingZone(FarmingZoneEvent event) {
    Player player = event.getPlayer();
    boolean stored = event.isAddedToStorage();
    int items = event.getTotalItemsHarvested();
    
    // Custom logic here
    if (stored) {
        // Reward player for using storage
        player.sendMessage("§a+10 Farming XP for using storage!");
    }
}
```

---

## 🎉 **Kết Luận**

ExtraStorage Integration mang lại:

- **Seamless farming experience** với auto storage
- **Reduced server lag** do ít item entities
- **Better organization** với automatic filtering
- **Enhanced gameplay** với storage automation
- **Performance optimized** implementation

**Perfect for:**
- Farming servers với large-scale agriculture
- Survival servers muốn reduce item lag
- Economy servers với organized storage systems
- Any server sử dụng ExtraStorage plugin

**Integration hoàn toàn tự động và transparent cho players!** 📦🌾⚡
