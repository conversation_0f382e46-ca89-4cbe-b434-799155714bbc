package shyrcs.Ability;

import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Custom event được gọi khi Mythic Drop Boost được kích ho<PERSON>t
 */
public class MythicDropBoostEvent extends Event {
    private static final HandlerList handlers = new HandlerList();
    
    private final Player player;
    private final Entity mythicMob;
    private final String mobType;
    private final double boostPercent;
    private final int originalDropCount;
    private final int boostedDropCount;
    
    public MythicDropBoostEvent(Player player, Entity mythicMob, String mobType, 
                               double boostPercent, int originalDropCount, int boostedDropCount) {
        this.player = player;
        this.mythicMob = mythicMob;
        this.mobType = mobType;
        this.boostPercent = boostPercent;
        this.originalDropCount = originalDropCount;
        this.boostedDropCount = boostedDropCount;
    }
    
    /**
     * Lấy player đã kill mob
     */
    public Player getPlayer() {
        return player;
    }
    
    /**
     * Lấy Mythic<PERSON>ob entity
     */
    public Entity getMythicMob() {
        return mythicMob;
    }
    
    /**
     * Lấy loại mob (MythicMob type)
     */
    public String getMobType() {
        return mobType;
    }
    
    /**
     * Lấy % boost được áp dụng
     */
    public double getBoostPercent() {
        return boostPercent;
    }
    
    /**
     * Lấy số lượng drop gốc
     */
    public int getOriginalDropCount() {
        return originalDropCount;
    }
    
    /**
     * Lấy số lượng drop sau khi boost
     */
    public int getBoostedDropCount() {
        return boostedDropCount;
    }
    
    /**
     * Lấy số lượng drop được thêm
     */
    public int getBonusDropCount() {
        return boostedDropCount - originalDropCount;
    }
    
    @Override
    public HandlerList getHandlers() {
        return handlers;
    }
    
    public static HandlerList getHandlerList() {
        return handlers;
    }
}
