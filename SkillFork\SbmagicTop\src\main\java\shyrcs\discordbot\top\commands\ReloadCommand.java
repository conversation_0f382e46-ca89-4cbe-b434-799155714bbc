package shyrcs.discordbot.top.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import shyrcs.discordbot.top.SbmagicTopPlugin;

public class ReloadCommand implements CommandExecutor {
    
    private final SbmagicTopPlugin plugin;
    
    public ReloadCommand(SbmagicTopPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("sbmagictop.reload")) {
            sender.sendMessage("§cBạn không có quyền sử dụng lệnh này!");
            return true;
        }

        // Check for subcommands
        if (args.length > 0) {
            String subCommand = args[0].toLowerCase();

            switch (subCommand) {
                case "webhook":
                    return handleWebhookCommand(sender, args);
                case "status":
                    return handleStatusCommand(sender);
                case "update":
                    return handleUpdateCommand(sender);
                default:
                    sender.sendMessage("§c[SbmagicTop] Lệnh con không hợp lệ!");
                    sender.sendMessage("§e/sbmagictop reload - Reload toàn bộ config");
                    sender.sendMessage("§e/sbmagictop webhook - Quản lý webhook");
                    sender.sendMessage("§e/sbmagictop status - Xem trạng thái");
                    sender.sendMessage("§e/sbmagictop update - Cập nhật webhook ngay");
                    return true;
            }
        }

        try {
            // Use the new reload method
            plugin.reloadPlugin();

            sender.sendMessage("§a[SbmagicTop] Đã reload config và webhook thành công!");
            plugin.getLogger().info("Plugin đã được reload bởi " + sender.getName());

        } catch (Exception e) {
            sender.sendMessage("§c[SbmagicTop] Có lỗi khi reload: " + e.getMessage());
            plugin.getLogger().severe("Lỗi khi reload: " + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }

    private boolean handleWebhookCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage("§e[SbmagicTop] Webhook Commands:");
            sender.sendMessage("§e/sbmagictop webhook status - Xem trạng thái webhook");
            sender.sendMessage("§e/sbmagictop webhook update - Cập nhật webhook ngay");
            sender.sendMessage("§e/sbmagictop webhook clear - Xóa message IDs");
            return true;
        }

        String webhookAction = args[1].toLowerCase();

        switch (webhookAction) {
            case "status":
                if (plugin.getScheduledUpdateManager() != null) {
                    sender.sendMessage("§a[SbmagicTop] " + plugin.getScheduledUpdateManager().getStatus());
                } else {
                    sender.sendMessage("§c[SbmagicTop] Webhook manager chưa được khởi tạo!");
                }
                break;

            case "update":
                if (plugin.getScheduledUpdateManager() != null) {
                    plugin.getScheduledUpdateManager().triggerManualUpdate();
                    sender.sendMessage("§a[SbmagicTop] Đã trigger cập nhật webhook!");
                } else {
                    sender.sendMessage("§c[SbmagicTop] Webhook manager chưa được khởi tạo!");
                }
                break;

            case "clear":
                if (plugin.getWebhookManager() != null) {
                    plugin.getWebhookManager().clearMessageIds();
                    sender.sendMessage("§a[SbmagicTop] Đã xóa tất cả message IDs!");
                } else {
                    sender.sendMessage("§c[SbmagicTop] Webhook manager chưa được khởi tạo!");
                }
                break;

            default:
                sender.sendMessage("§c[SbmagicTop] Webhook action không hợp lệ!");
                break;
        }

        return true;
    }

    private boolean handleStatusCommand(CommandSender sender) {
        sender.sendMessage("§a[SbmagicTop] Plugin Status:");
        sender.sendMessage("§e- JDA: " + (plugin.getJDA() != null ? "Connected" : "Disconnected"));
        sender.sendMessage("§e- Webhook Manager: " + (plugin.getWebhookManager() != null ? "Loaded" : "Not loaded"));
        sender.sendMessage("§e- Update Manager: " + (plugin.getScheduledUpdateManager() != null ? "Loaded" : "Not loaded"));

        if (plugin.getScheduledUpdateManager() != null) {
            sender.sendMessage("§e- Auto Update: " + (plugin.getScheduledUpdateManager().isRunning() ? "Running" : "Stopped"));
            sender.sendMessage("§e- Next Update: " + plugin.getScheduledUpdateManager().getNextUpdateTime());
        }

        if (plugin.getWebhookManager() != null) {
            sender.sendMessage("§e- Webhook Enabled: " + plugin.getWebhookManager().isWebhookEnabled());
            sender.sendMessage("§e- Message IDs: " + plugin.getWebhookManager().getMessageIds().size());
        }

        return true;
    }

    private boolean handleUpdateCommand(CommandSender sender) {
        if (plugin.getScheduledUpdateManager() != null) {
            plugin.getScheduledUpdateManager().triggerManualUpdate();
            sender.sendMessage("§a[SbmagicTop] Đã trigger cập nhật webhook thủ công!");
        } else {
            sender.sendMessage("§c[SbmagicTop] Update manager chưa được khởi tạo!");
        }
        return true;
    }
}
