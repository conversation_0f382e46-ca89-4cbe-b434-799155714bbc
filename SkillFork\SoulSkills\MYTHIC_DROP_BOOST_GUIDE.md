# 🐉 HƯỚNG DẪN MYTHIC DROP BOOST ABILITY

## 📋 **Tổng Quan**

Mythic Drop Boost là một custom MMOItems stat mạnh mẽ cho phép tăng tỷ lệ drop item từ MythicMob:

- ✅ **Tăng drop rate** - Tăng % drop của tất cả items từ MythicMob
- ✅ **Stack được** - <PERSON><PERSON> thể stack từ nhiều items (weapon, armor, accessories)
- ✅ **Flexible** - Áp dụng cho mọi loại MythicMob
- ✅ **Performance optimized** - Sử dụng reflection để hook vào MythicMobs
- ✅ **Event system** - Có custom event để track boost

**Icon trong GUI:** 🥚 **Dragon Egg**

---

## 🎯 **Cách Sử Dụng**

### **1. Thêm Stat vào Item**

1. Mở MMOItems GUI: `/mmoitems edit <type> <id>`
2. Tì<PERSON> và click vào stat **"Mythic Drop Boost"** (icon Dragon Egg)
3. <PERSON><PERSON><PERSON><PERSON> giá trị % trong chat

### **2. <PERSON><PERSON><PERSON>ợ<PERSON>**

| <PERSON><PERSON><PERSON> | <PERSON>ô <PERSON> | Hiệu Q<PERSON>ả |
|---------|-------|----------|
| `0` | Không boost | Không thay đổi drop |
| `25` | Boost 25% | Tăng 25% drop rate |
| `50` | Boost 50% | Tăng 50% drop rate |
| `100` | Boost 100% | Tăng 100% drop rate (double) |
| `200` | Boost 200% | Tăng 200% drop rate (triple) |

**Giới hạn:** 0-1000% (khuyến nghị không quá 200%)

---

## 🌟 **Ví Dụ Thực Tế**

### **⚔️ Dragon Slayer Sword**
```bash
/mmoitems edit weapon dragon_sword
# Click vào "Mythic Drop Boost" và nhập: 75
```
- **Hiệu quả:** Tăng 75% drop rate khi kill MythicMob
- **Lore:** `§7Mythic Drop Boost: §a75.0%`

### **🛡️ Legendary Armor Set**
```bash
# Helmet
/mmoitems edit armor legend_helmet
# Mythic Drop Boost: 25

# Chestplate  
/mmoitems edit armor legend_chest
# Mythic Drop Boost: 25

# Leggings
/mmoitems edit armor legend_legs
# Mythic Drop Boost: 25

# Boots
/mmoitems edit armor legend_boots
# Mythic Drop Boost: 25
```
- **Tổng boost:** 100% (25% x 4 pieces)
- **Hiệu quả:** Double drop rate khi mặc full set

### **💍 Drop Boost Ring**
```bash
/mmoitems edit accessory boost_ring
# Mythic Drop Boost: 50
```
- **Hiệu quả:** Tăng 50% drop rate
- **Stack:** Có thể combine với weapon/armor

---

## ⚡ **Cách Hoạt Động**

### **🎯 Drop Calculation**
1. **Player kill MythicMob** → Trigger event
2. **Scan player equipment** → Tính tổng boost %
3. **Apply boost chance** → Roll random cho mỗi drop
4. **Increase drop amount** → Tăng số lượng item drop

### **📊 Stack System**
```
Total Boost = Weapon Boost + Armor Boost + Accessory Boost

Ví dụ:
- Weapon: 50%
- Helmet: 15% 
- Chestplate: 15%
- Ring: 25%
Total: 105% boost
```

### **🎲 Boost Mechanics**
- **Chance-based:** Mỗi drop có % chance được boost
- **Amount increase:** Tăng số lượng item drop
- **All drops affected:** Áp dụng cho tất cả drops của mob

---

## 🔧 **Technical Details**

### **Supported Items**
- ✅ **Weapons** - Sword, Axe, Bow, etc.
- ✅ **Tools** - Pickaxe, Shovel, etc.
- ✅ **Armor** - Helmet, Chestplate, Leggings, Boots
- ✅ **Accessories** - Ring, Necklace, etc.

### **MythicMob Integration**
- Hook vào `MythicMobDeathEvent`
- Sử dụng reflection để modify drops
- Tương thích với mọi version MythicMobs

### **Performance**
- Reflection caching để tối ưu
- Chỉ process khi player có boost stat
- Minimal impact trên server performance

---

## 🎮 **Hướng Dẫn Tạo Item**

### **Bước 1: Tạo Base Item**
```bash
/mmoitems create weapon mythic_slayer
```

### **Bước 2: Edit Item**
```bash
/mmoitems edit weapon mythic_slayer
```

### **Bước 3: Thêm Mythic Drop Boost**
1. Click vào **Mythic Drop Boost** (🥚 icon)
2. Nhập giá trị: `60`

### **Bước 4: Tùy chỉnh Item**
```bash
# Set tên và material
/mmoitems modify weapon mythic_slayer set display-name "§6§lMythic Slayer"
/mmoitems modify weapon mythic_slayer set material NETHERITE_SWORD

# Thêm lore mô tả
/mmoitems modify weapon mythic_slayer add-lore "§7Specialized weapon for hunting MythicMobs"
/mmoitems modify weapon mythic_slayer add-lore "§7Increases drop rates significantly"
```

### **Bước 5: Test**
```bash
/mmoitems give <player> weapon mythic_slayer
```

---

## 📊 **Drop Rate Examples**

### **Normal vs Boosted**
```
Normal MythicMob Drop:
- Rare Item: 5% chance
- Epic Item: 1% chance

With 100% Boost:
- Rare Item: ~10% chance  
- Epic Item: ~2% chance

With 200% Boost:
- Rare Item: ~15% chance
- Epic Item: ~3% chance
```

### **Stack Example**
```
Equipment Setup:
- Weapon: 75% boost
- Armor Set: 100% boost (25% x 4)
- Accessories: 50% boost
Total: 225% boost

Result: Massive increase in drop rates!
```

---

## ⚠️ **Lưu Ý Quan Trọng**

1. **Chỉ áp dụng cho MythicMob** - Không ảnh hưởng vanilla mobs
2. **Cần MythicMobs plugin** - Plugin phải được cài đặt
3. **Performance impact** - Boost quá cao có thể lag server
4. **Balance carefully** - Khuyến nghị test trước khi release
5. **Stack limit** - Không có giới hạn stack, cần balance manual

---

## 🐛 **Troubleshooting**

### **Boost không hoạt động?**
- Kiểm tra MythicMobs plugin đã cài đặt chưa
- Đảm bảo mob là MythicMob (không phải vanilla)
- Check console có lỗi reflection không

### **Drop rate quá cao?**
- Giảm boost % trong item stats
- Kiểm tra tổng boost từ tất cả equipment
- Consider balance với economy

### **Performance issues?**
- Giảm số lượng players có boost items
- Optimize MythicMob drop tables
- Monitor server TPS

---

## 🎉 **Kết Luận**

Mythic Drop Boost Ability mang lại:
- **Enhanced farming experience** với increased drop rates
- **Flexible customization** cho từng item type
- **Stack system** cho build diversity
- **Performance optimized** implementation
- **Easy to use** MMOItems integration

**Perfect for:**
- RPG servers với rare item farming
- Economy servers cần boost certain drops  
- Adventure servers với MythicMob bosses

Chúc bạn farming hiệu quả với Mythic Drop Boost! 🐉⚡
