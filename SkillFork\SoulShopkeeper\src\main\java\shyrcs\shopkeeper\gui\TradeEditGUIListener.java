package shyrcs.shopkeeper.gui;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

/**
 * Dedicated listener for Trade Edit GUI
 * Based on VillagerTradeEdit approach for better item handling
 */
public class TradeEditGUIListener implements Listener {
    
    private final SoulShopkeeperPlugin plugin;
    
    public TradeEditGUIListener(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();
        
        // Only handle Trade Editor GUIs
        if (!title.contains("Trade Editor")) {
            return;
        }
        
        // Get session
        ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
        if (session == null || session.getType() != ShopkeeperGUIManager.GUIType.TRADE_EDIT) {
            return;
        }
        
        handleTradeEditClick(event, player, session);
    }
    
    private void handleTradeEditClick(InventoryClickEvent event, Player player, ShopkeeperGUIManager.GUISession session) {
        int slot = event.getSlot();
        Inventory inventory = event.getInventory();
        SoulShopkeeper shopkeeper = session.getShopkeeper();
        
        if (shopkeeper == null) {
            player.closeInventory();
            return;
        }
        
        // Handle different slot types
        if (isTradeSlot(slot)) {
            // Trade slots (10, 12, 16) - allow item interaction
            handleTradeSlotClick(event, player, slot);
        } else if (isControlButton(slot)) {
            // Control buttons - handle actions
            event.setCancelled(true);
            handleControlButtonClick(event, player, session, slot);
        } else {
            // Other slots - cancel interaction
            event.setCancelled(true);
        }
    }
    
    private boolean isTradeSlot(int slot) {
        return slot == 10 || slot == 12 || slot == 16;
    }
    
    private boolean isControlButton(int slot) {
        return slot == 22 || slot == 24 || slot == 26;
    }
    
    private void handleTradeSlotClick(InventoryClickEvent event, Player player, int slot) {
        // Prevent ALL item interactions that could cause duplication
        event.setCancelled(true);

        ItemStack cursor = event.getCursor();
        ItemStack current = event.getInventory().getItem(slot);

        if (event.isLeftClick()) {
            if (cursor != null && !cursor.getType().isAir()) {
                // Player is placing an item
                ItemStack toPlace = cursor.clone();
                toPlace.setAmount(1); // Only allow 1 item per slot

                event.getInventory().setItem(slot, toPlace);
                player.setItemOnCursor(null);

                String itemName = getItemDisplayName(toPlace);
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&7Item placed: &f" + itemName);
            } else if (current != null && !current.getType().isAir()) {
                // Player is removing an item
                event.getInventory().setItem(slot, null);
                player.setItemOnCursor(current);

                String itemName = getItemDisplayName(current);
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&7Item removed: &f" + itemName);
            }
        } else if (event.isRightClick()) {
            if (current != null && !current.getType().isAir()) {
                // Right click to remove item
                event.getInventory().setItem(slot, null);
                player.setItemOnCursor(current);

                String itemName = getItemDisplayName(current);
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&7Item removed: &f" + itemName);
            }
        }
    }
    
    private void handleControlButtonClick(InventoryClickEvent event, Player player, 
                                        ShopkeeperGUIManager.GUISession session, int slot) {
        SoulShopkeeper shopkeeper = session.getShopkeeper();
        
        switch (slot) {
            case 22: // Save Trade
                saveTrade(player, event.getInventory(), shopkeeper, session.getTradeIndex());
                break;
                
            case 24: // Back/Cancel
                plugin.getGUIManager().openEditGUI(player, shopkeeper);
                break;
                
            case 26: // Delete Trade
                deleteTrade(player, shopkeeper, session.getTradeIndex());
                break;
        }
    }
    
    private void saveTrade(Player player, Inventory gui, SoulShopkeeper shopkeeper, int tradeIndex) {
        // Get items from trade slots
        ItemStack ingredient1 = gui.getItem(10);
        ItemStack ingredient2 = gui.getItem(12);
        ItemStack result = gui.getItem(16);

        // Debug logging
        plugin.getLogger().info("Saving trade - Ingredient1: " + (ingredient1 != null ? ingredient1.getType() : "null") +
                               ", Ingredient2: " + (ingredient2 != null ? ingredient2.getType() : "null") +
                               ", Result: " + (result != null ? result.getType() : "null"));

        // Validate required items
        if (result == null || result.getType().isAir()) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cResult item is required!");
            return;
        }

        if (ingredient1 == null || ingredient1.getType().isAir()) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cFirst ingredient is required!");
            return;
        }

        try {
            // Store items with detailed logging
            plugin.getLogger().info("Storing items...");
            String resultId = plugin.getMMOItemStorage().storeMMOItem(result);
            plugin.getLogger().info("Result stored with ID: " + resultId);

            String ingredient1Id = plugin.getMMOItemStorage().storeMMOItem(ingredient1);
            plugin.getLogger().info("Ingredient1 stored with ID: " + ingredient1Id);

            String ingredient2Id = null;
            if (ingredient2 != null && !ingredient2.getType().isAir()) {
                ingredient2Id = plugin.getMMOItemStorage().storeMMOItem(ingredient2);
                plugin.getLogger().info("Ingredient2 stored with ID: " + ingredient2Id);
            }

            if (resultId == null || ingredient1Id == null) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cFailed to store items! Please try again.");
                plugin.getLogger().warning("Failed to store items - resultId: " + resultId + ", ingredient1Id: " + ingredient1Id);
                return;
            }

            // Create trade
            ShopkeeperTrade trade = new ShopkeeperTrade(resultId, ingredient1Id, ingredient2Id, -1, 0, true);
            plugin.getLogger().info("Created trade object");

            // Add or update trade
            if (tradeIndex >= 0 && tradeIndex < shopkeeper.getTrades().size()) {
                shopkeeper.removeTrade(tradeIndex);
                shopkeeper.getTrades().add(tradeIndex, trade);
                plugin.getLogger().info("Updated existing trade at index: " + tradeIndex);
            } else {
                shopkeeper.addTrade(trade);
                plugin.getLogger().info("Added new trade");
            }

            // Save to database
            saveShopkeeperToDatabase(shopkeeper);
            plugin.getLogger().info("Saved shopkeeper to database");

            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&aTrade saved successfully!");

            // Return to edit GUI
            plugin.getGUIManager().openEditGUI(player, shopkeeper);

        } catch (Exception e) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cFailed to save trade: " + e.getMessage());
            plugin.getLogger().severe("Failed to save trade: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void deleteTrade(Player player, SoulShopkeeper shopkeeper, int tradeIndex) {
        if (tradeIndex >= 0 && tradeIndex < shopkeeper.getTrades().size()) {
            shopkeeper.removeTrade(tradeIndex);
            saveShopkeeperToDatabase(shopkeeper);
            
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + 
                                   "&aTrade deleted!");
            plugin.getGUIManager().openEditGUI(player, shopkeeper);
        }
    }
    
    private void saveShopkeeperToDatabase(SoulShopkeeper shopkeeper) {
        if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
            shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper = 
                (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
            wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
        }
    }
    
    private String getItemDisplayName(ItemStack item) {
        if (item == null) return "Unknown";
        
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return MessageUtils.stripColors(item.getItemMeta().getDisplayName());
        }
        
        // Check if it's an MMOItem
        String mmoInfo = plugin.getMMOItemStorage().getMMOItemInfo(item);
        if (mmoInfo != null) {
            return mmoInfo;
        }
        
        return item.getType().name().toLowerCase().replace('_', ' ');
    }
    
    @EventHandler
    public void onInventoryDrag(InventoryDragEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        String title = event.getView().getTitle();
        if (!title.contains("Trade Editor")) {
            return;
        }

        // Cancel ALL drag events in Trade Editor to prevent duplication
        event.setCancelled(true);
    }
    
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }
        
        String title = event.getView().getTitle();
        if (title.contains("Trade Editor")) {
            Player player = (Player) event.getPlayer();
            
            // Clean up session after a short delay
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                plugin.getGUIManager().removeSession(player.getUniqueId());
            }, 1L);
        }
    }
}
