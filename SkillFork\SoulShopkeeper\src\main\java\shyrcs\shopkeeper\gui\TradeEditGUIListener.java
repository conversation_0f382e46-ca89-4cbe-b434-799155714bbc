package shyrcs.shopkeeper.gui;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

/**
 * Simple and effective Trade Edit GUI Listener
 * Completely recoded for reliability
 */
public class TradeEditGUIListener implements Listener {

    private final SoulShopkeeperPlugin plugin;

    public TradeEditGUIListener(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();

        // Only handle Trade Editor GUIs
        if (!title.contains("Trade Editor")) {
            return;
        }

        plugin.getLogger().info("TradeEdit GUI click detected - Slot: " + event.getSlot() +
                               ", Click: " + event.getClick() + ", Title: " + title);

        // Get session
        ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
        if (session == null) {
            plugin.getLogger().warning("No session found for player: " + player.getName());
            return;
        }

        if (session.getType() != ShopkeeperGUIManager.GUIType.TRADE_EDIT) {
            plugin.getLogger().warning("Session type mismatch for player: " + player.getName() +
                                     ", expected TRADE_EDIT but got: " + session.getType());
            return;
        }

        plugin.getLogger().info("Valid TRADE_EDIT session found for player: " + player.getName());

        handleTradeEditClick(event, player, session);
    }
    
    private void handleTradeEditClick(InventoryClickEvent event, Player player, ShopkeeperGUIManager.GUISession session) {
        int slot = event.getSlot();
        SoulShopkeeper shopkeeper = session.getShopkeeper();

        if (shopkeeper == null) {
            plugin.getLogger().warning("Shopkeeper is null in session");
            player.closeInventory();
            return;
        }

        plugin.getLogger().info("Handling click in slot: " + slot);

        // Trade slots: 10 (ingredient1), 12 (ingredient2), 16 (result)
        if (slot == 10 || slot == 12 || slot == 16) {
            handleTradeSlotClick(event, player, slot);
            return;
        }

        // Control buttons
        if (slot == 22) { // Save
            event.setCancelled(true);
            saveTrade(player, event.getInventory(), shopkeeper, session.getTradeIndex());
            return;
        }

        if (slot == 24) { // Back
            event.setCancelled(true);
            plugin.getGUIManager().openEditGUI(player, shopkeeper);
            return;
        }

        if (slot == 26) { // Delete
            event.setCancelled(true);
            deleteTrade(player, shopkeeper, session.getTradeIndex());
            return;
        }

        // All other slots - cancel
        event.setCancelled(true);
    }
    
    private void handleTradeSlotClick(InventoryClickEvent event, Player player, int slot) {
        plugin.getLogger().info("Trade slot click - Slot: " + slot + ", Click: " + event.getClick());

        // For trade slots, we want to allow normal item interaction
        // But prevent shift-clicking to avoid duplication
        if (event.getClick() == ClickType.SHIFT_LEFT || event.getClick() == ClickType.SHIFT_RIGHT) {
            event.setCancelled(true);
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cShift-clicking is disabled to prevent item duplication!");
            return;
        }

        // Allow normal left/right click - let Minecraft handle it naturally
        // This is the simplest and most reliable approach
        plugin.getLogger().info("Allowing natural item interaction for slot: " + slot);
    }
    
    private void deleteTrade(Player player, SoulShopkeeper shopkeeper, int tradeIndex) {
        plugin.getLogger().info("Deleting trade at index: " + tradeIndex);

        if (tradeIndex >= 0 && tradeIndex < shopkeeper.getTrades().size()) {
            shopkeeper.removeTrade(tradeIndex);
            saveShopkeeperToDatabase(shopkeeper);

            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&aTrade deleted successfully!");
            plugin.getGUIManager().openEditGUI(player, shopkeeper);
        } else {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cInvalid trade index!");
        }
    }
    
    private void saveTrade(Player player, Inventory gui, SoulShopkeeper shopkeeper, int tradeIndex) {
        plugin.getLogger().info("=== SAVE TRADE START ===");

        // Get items from trade slots
        ItemStack ingredient1 = gui.getItem(10);
        ItemStack ingredient2 = gui.getItem(12);
        ItemStack result = gui.getItem(16);

        plugin.getLogger().info("Items retrieved:");
        plugin.getLogger().info("- Ingredient1 (slot 10): " + (ingredient1 != null ? ingredient1.getType() + " x" + ingredient1.getAmount() : "null"));
        plugin.getLogger().info("- Ingredient2 (slot 12): " + (ingredient2 != null ? ingredient2.getType() + " x" + ingredient2.getAmount() : "null"));
        plugin.getLogger().info("- Result (slot 16): " + (result != null ? result.getType() + " x" + result.getAmount() : "null"));

        // Validate required items
        if (result == null || result.getType() == Material.AIR) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cResult item is required!");
            plugin.getLogger().warning("Save failed: No result item");
            return;
        }

        if (ingredient1 == null || ingredient1.getType() == Material.AIR) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cFirst ingredient is required!");
            plugin.getLogger().warning("Save failed: No first ingredient");
            return;
        }

        try {
            plugin.getLogger().info("Starting item storage...");

            // Store result item
            String resultId = plugin.getMMOItemStorage().storeMMOItem(result);
            plugin.getLogger().info("Result item stored with ID: " + resultId);

            // Store ingredient1
            String ingredient1Id = plugin.getMMOItemStorage().storeMMOItem(ingredient1);
            plugin.getLogger().info("Ingredient1 stored with ID: " + ingredient1Id);

            // Store ingredient2 if present
            String ingredient2Id = null;
            if (ingredient2 != null && ingredient2.getType() != Material.AIR) {
                ingredient2Id = plugin.getMMOItemStorage().storeMMOItem(ingredient2);
                plugin.getLogger().info("Ingredient2 stored with ID: " + ingredient2Id);
            } else {
                plugin.getLogger().info("No second ingredient to store");
            }

            // Validate storage success
            if (resultId == null || ingredient1Id == null) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cFailed to store items! Please try again.");
                plugin.getLogger().severe("Item storage failed - resultId: " + resultId + ", ingredient1Id: " + ingredient1Id);
                return;
            }

            // Create trade object
            ShopkeeperTrade trade = new ShopkeeperTrade(resultId, ingredient1Id, ingredient2Id, -1, 0, true);
            plugin.getLogger().info("Created trade object successfully");

            // Add or update trade
            if (tradeIndex >= 0 && tradeIndex < shopkeeper.getTrades().size()) {
                shopkeeper.removeTrade(tradeIndex);
                shopkeeper.getTrades().add(tradeIndex, trade);
                plugin.getLogger().info("Updated existing trade at index: " + tradeIndex);
            } else {
                shopkeeper.addTrade(trade);
                plugin.getLogger().info("Added new trade (total trades: " + shopkeeper.getTrades().size() + ")");
            }

            // Save to database
            saveShopkeeperToDatabase(shopkeeper);
            plugin.getLogger().info("Shopkeeper saved to database");

            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&aTrade saved successfully!");

            // Return to edit GUI
            plugin.getGUIManager().openEditGUI(player, shopkeeper);
            plugin.getLogger().info("=== SAVE TRADE SUCCESS ===");

        } catch (Exception e) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cFailed to save trade: " + e.getMessage());
            plugin.getLogger().severe("=== SAVE TRADE FAILED ===");
            plugin.getLogger().severe("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void saveShopkeeperToDatabase(SoulShopkeeper shopkeeper) {
        try {
            if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                    (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
                plugin.getLogger().info("Database save completed successfully");
            } else {
                plugin.getLogger().warning("DataManager is not DatabaseDataManagerWrapper type");
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to save shopkeeper to database: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @EventHandler
    public void onInventoryDrag(InventoryDragEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        String title = event.getView().getTitle();
        if (!title.contains("Trade Editor")) {
            return;
        }

        plugin.getLogger().info("Drag event in Trade Editor - cancelling to prevent duplication");

        // Cancel ALL drag events to prevent item duplication
        event.setCancelled(true);

        Player player = (Player) event.getWhoClicked();
        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                               "&cDragging items is disabled. Please click to place items.");
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }

        String title = event.getView().getTitle();
        if (title.contains("Trade Editor")) {
            Player player = (Player) event.getPlayer();
            plugin.getLogger().info("Trade Editor closed by player: " + player.getName());

            // Clean up session safely
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                plugin.getGUIManager().removeSessionSafely(player.getUniqueId(),
                                                          ShopkeeperGUIManager.GUIType.TRADE_EDIT);
                plugin.getLogger().info("Session cleaned up for player: " + player.getName());
            }, 1L);
        }
    }
}
