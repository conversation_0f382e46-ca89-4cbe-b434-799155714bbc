package shyrcs.discordbot.top;

import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.configuration.file.FileConfiguration;
import net.dv8tion.jda.api.JDA;
import net.dv8tion.jda.api.JDABuilder;
import net.dv8tion.jda.api.requests.GatewayIntent;
import net.dv8tion.jda.api.interactions.commands.build.Commands;
import net.dv8tion.jda.api.interactions.commands.build.SlashCommandData;
import shyrcs.discordbot.top.commands.TopSlashCommand;
import shyrcs.discordbot.top.commands.ReloadCommand;
import shyrcs.discordbot.top.managers.ConfigManager;
import shyrcs.discordbot.top.managers.PlaceholderManager;
import shyrcs.discordbot.top.managers.WebhookManager;
import shyrcs.discordbot.top.managers.ScheduledUpdateManager;

import java.util.logging.Logger;

public class SbmagicTopPlugin extends JavaPlugin {

    private static SbmagicTopPlugin instance;
    private JDA jda;
    private ConfigManager configManager;
    private PlaceholderManager placeholderManager;
    private WebhookManager webhookManager;
    private ScheduledUpdateManager scheduledUpdateManager;
    private Logger logger;

    @Override
    public void onEnable() {
        instance = this;
        logger = getLogger();

        // Lưu config mặc định
        saveDefaultConfig();

        // Khởi tạo managers
        configManager = new ConfigManager(this);
        placeholderManager = new PlaceholderManager(this);

        // Đăng ký commands
        getCommand("sbmagictop").setExecutor(new ReloadCommand(this));

        // Khởi tạo Discord bot
        initializeDiscordBot();

        // Khởi tạo webhook manager sau khi JDA đã sẵn sàng
        if (jda != null) {
            webhookManager = new WebhookManager(this);
            scheduledUpdateManager = new ScheduledUpdateManager(this, webhookManager);
            scheduledUpdateManager.start();
        }

        logger.info("SbmagicTop plugin enabled");
    }

    @Override
    public void onDisable() {
        // Stop scheduled updates
        if (scheduledUpdateManager != null) {
            scheduledUpdateManager.stop();
        }

        // Shutdown JDA
        if (jda != null) {
            jda.shutdown();
        }

        logger.info("SbmagicTop plugin disabled");
    }

    private void initializeDiscordBot() {
        FileConfiguration config = getConfig();
        String token = config.getString("discord.token");
        String guildId = config.getString("discord.guild-id");

        if (token == null || token.equals("YOUR_BOT_TOKEN_HERE")) {
            logger.severe("Please configure Discord bot token in config.yml!");
            return;
        }

        if (guildId == null || guildId.equals("YOUR_GUILD_ID_HERE")) {
            logger.severe("Please configure Guild ID in config.yml!");
            return;
        }

        try {
            logger.info("Loading config: " + getConfig().getString("discord.guild-id", "default"));

            jda = JDABuilder.createDefault(token)
                    .enableIntents(GatewayIntent.GUILD_MESSAGES, GatewayIntent.MESSAGE_CONTENT)
                    .build();

            jda.awaitReady();

            // Đăng ký event listeners
            TopSlashCommand topCommand = new TopSlashCommand(this);
            jda.addEventListener(topCommand);

            // Đăng ký guild commands
            SlashCommandData topCommandData = TopSlashCommand.getCommandData();
            SlashCommandData pingCommand = Commands.slash("ping", "Test bot response");

            jda.getGuildById(guildId).updateCommands()
                    .addCommands(topCommandData, pingCommand)
                    .queue(
                        success -> {
                            // Guild commands registered
                        },
                        error -> {
                            logger.severe("Error registering guild commands: " + error.getMessage());
                        }
                    );

            // Đăng ký global commands
            SlashCommandData topCommandGlobal = TopSlashCommand.getCommandData();
            SlashCommandData pingCommandGlobal = Commands.slash("ping", "Test bot response");

            jda.updateCommands()
                    .addCommands(topCommandGlobal, pingCommandGlobal)
                    .queue(
                        success -> {
                            // Global commands registered
                        },
                        error -> {
                            logger.severe("Error registering global commands: " + error.getMessage());
                        }
                    );

        } catch (Exception e) {
            logger.severe("Failed to initialize Discord bot: " + e.getMessage());
        }
    }

    public static SbmagicTopPlugin getInstance() {
        return instance;
    }

    public JDA getJDA() {
        return jda;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public PlaceholderManager getPlaceholderManager() {
        return placeholderManager;
    }

    public WebhookManager getWebhookManager() {
        return webhookManager;
    }

    public ScheduledUpdateManager getScheduledUpdateManager() {
        return scheduledUpdateManager;
    }

    public void reloadPlugin() {
        // Reload config
        reloadConfig();

        // Reload managers
        if (configManager != null) {
            configManager.reloadConfigs();
        }

        // Restart scheduled updates
        if (scheduledUpdateManager != null) {
            scheduledUpdateManager.restart();
            scheduledUpdateManager.triggerReloadUpdate();
        }

        logger.info("Plugin reloaded successfully");
    }
}