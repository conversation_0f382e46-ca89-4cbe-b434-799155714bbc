# Purple Prison Dome - T<PERSON><PERSON> lồng giam tím với damage liê<PERSON> tục (COMPLETELY FIXED)
purple_prison_dome:
  public: true
  modifiers:
    - damage
    - duration
  mechanics:
    # Âm thanh kích hoạt skill
    activation_sound:
      type: sound
      sound: BLOCK_BEACON_ACTIVATE
      volume: 2
      pitch: 0.5

    # Particle nổ khi kích hoạt
    activation_explosion:
      type: particle
      particle: EXPLOSION_LARGE
      amount: 5
      target:
        type: caster
        position: BODY

    # Bắt đầu prison dome loop trong 7 giây
    start_prison_dome:
      type: script
      name: prison_dome_main_loop

# Main loop cho prison dome - chạy trong 7 giây
prison_dome_main_loop:
  mechanics:
    # Lần 1 - ngay lập tức
    first_iteration:
      type: script
      name: prison_dome_single_iteration
      delay: 0

    # Lần 2 - sau 1 giây
    second_iteration:
      type: script
      name: prison_dome_single_iteration
      delay: 20

    # Lần 3 - sau 2 giây
    third_iteration:
      type: script
      name: prison_dome_single_iteration
      delay: 40

    # <PERSON>ần 4 - sau 3 gi<PERSON><PERSON>
    fourth_iteration:
      type: script
      name: prison_dome_single_iteration
      delay: 60

    # <PERSON>ần 5 - sau 4 giây
    fifth_iteration:
      type: script
      name: prison_dome_single_iteration
      delay: 80

    # Lần 6 - sau 5 giây
    sixth_iteration:
      type: script
      name: prison_dome_single_iteration
      delay: 100

    # Lần 7 - sau 6 giây
    seventh_iteration:
      type: script
      name: prison_dome_single_iteration
      delay: 120

# Một lần iteration cho prison dome
prison_dome_single_iteration:
  mechanics:
    # Tạo vòng tròn particle dome
    dome_circle:
      type: particle
      particle: REDSTONE
      amount: 20
      x: 15
      y: 10
      z: 15
      data: 1,0,1 # Màu tím
      target:
        type: caster
        position: FEET

    # Particle enchantment ở giữa
    center_magic:
      type: particle
      particle: ENCHANTMENT_TABLE
      amount: 3
      target:
        type: caster
        position: BODY

    # Âm thanh môi trường
    ambient_sound:
      type: sound
      sound: BLOCK_CONDUIT_AMBIENT
      volume: 0.3
      pitch: 0.8
      target:
        type: caster

    # Damage tất cả trong phạm vi
    area_damage:
      type: damage
      amount: 20
      damage_type: MAGIC,SKILL,DOT
      ignore_armor: true
      ignore_resistances: true
      target:
        type: nearby_entities
        radius: 15
        ignore_caster: false

    # Particle damage
    damage_particles:
      type: particle
      particle: REDSTONE
      amount: 15
      x: 15
      y: 10
      z: 15
      data: 0.5,0,0.5 # Màu tím đậm
      target:
        type: caster
        position: FEET

    # Âm thanh damage
    damage_sound:
      type: sound
      sound: ENTITY_PLAYER_HURT
      volume: 0.8
      pitch: 1.0
      target:
        type: caster

# Version đơn giản hơn cho hiệu suất tốt hơn - COMPLETELY REWRITTEN
purple_prison_simple:
  public: true
  modifiers:
    - damage
  mechanics:
    # Kích hoạt
    cast_sound:
      type: sound
      sound: BLOCK_BEACON_ACTIVATE
      volume: 2
      pitch: 0.5

    cast_particles:
      type: particle
      particle: EXPLOSION_LARGE
      amount: 3

    # Bắt đầu simple prison loop
    start_simple_loop:
      type: script
      name: simple_prison_main_loop

# Main loop cho simple prison - chạy trong 7 giây
simple_prison_main_loop:
  mechanics:
    # Lần 1 - ngay lập tức
    first_iteration:
      type: script
      name: simple_prison_single_iteration
      delay: 0

    # Lần 2 - sau 1 giây
    second_iteration:
      type: script
      name: simple_prison_single_iteration
      delay: 20

    # Lần 3 - sau 2 giây
    third_iteration:
      type: script
      name: simple_prison_single_iteration
      delay: 40

    # Lần 4 - sau 3 giây
    fourth_iteration:
      type: script
      name: simple_prison_single_iteration
      delay: 60

    # Lần 5 - sau 4 giây
    fifth_iteration:
      type: script
      name: simple_prison_single_iteration
      delay: 80

    # Lần 6 - sau 5 giây
    sixth_iteration:
      type: script
      name: simple_prison_single_iteration
      delay: 100

    # Lần 7 - sau 6 giây
    seventh_iteration:
      type: script
      name: simple_prison_single_iteration
      delay: 120

# Một lần iteration cho simple prison
simple_prison_single_iteration:
  mechanics:
    # Tạo vòng tròn particle tím
    dome_outline:
      type: particle
      particle: REDSTONE
      amount: 15
      x: 15
      y: 8
      z: 15
      data: 1,0,1 # Màu tím
      target:
        type: caster
        position: FEET

    # Particle ở giữa dome
    center_effect:
      type: particle
      particle: ENCHANTMENT_TABLE
      amount: 3
      target:
        type: caster
        position: BODY

    # Âm thanh
    ambient_sound:
      type: sound
      sound: BLOCK_CONDUIT_AMBIENT
      volume: 0.2
      pitch: 0.9
      target:
        type: caster

    # Damage tất cả trong phạm vi
    area_damage:
      type: damage
      amount: 20
      damage_type: MAGIC,SKILL,DOT
      ignore_armor: true
      ignore_resistances: true
      target:
        type: nearby_entities
        radius: 15
        ignore_caster: false

    # Particle damage
    damage_particles:
      type: particle
      particle: REDSTONE
      amount: 20
      x: 15
      y: 8
      z: 15
      data: 0.8,0,0.8 # Màu tím đậm
      target:
        type: caster
        position: FEET

    # Âm thanh damage
    damage_sound:
      type: sound
      sound: ENTITY_PLAYER_HURT
      volume: 1
      pitch: 1.2
      target:
        type: caster

# Version alternative với true damage - COMPLETELY REWRITTEN
purple_prison_truedmg:
  public: true
  modifiers:
    - damage
  mechanics:
    # Kích hoạt
    cast_sound:
      type: sound
      sound: BLOCK_BEACON_ACTIVATE
      volume: 2
      pitch: 0.5

    cast_particles:
      type: particle
      particle: EXPLOSION_LARGE
      amount: 3

    # Bắt đầu loop chính cho 7 giây
    start_prison_loop:
      type: script
      name: truedmg_main_loop

# Loop chính chạy trong 7 giây
truedmg_main_loop:
  mechanics:
    # Tạo particle dome
    dome_particles:
      type: particle
      particle: REDSTONE
      amount: 25
      x: 15
      y: 10
      z: 15
      data: 0.6,0,0.9 # Màu tím
      target:
        type: caster
        position: FEET

    # Particle ở giữa
    center_particles:
      type: particle
      particle: ENCHANTMENT_TABLE
      amount: 3
      target:
        type: caster
        position: BODY

    # Damage tất cả trong phạm vi
    area_damage:
      type: damage
      amount: 25
      damage_type: MAGIC,SKILL,DOT
      ignore_armor: true
      ignore_resistances: true
      target:
        type: nearby_entities
        radius: 15
        ignore_caster: false

    # Âm thanh damage
    damage_sound:
      type: sound
      sound: ENTITY_PLAYER_HURT
      volume: 0.8
      pitch: 0.8
      target:
        type: caster

    # Chờ 1 giây trước lần tiếp theo
    wait_one_second:
      type: delay
      amount: 20

    # Lặp lại 6 lần nữa (tổng cộng 7 lần)
    repeat_loop:
      type: script
      name: truedmg_repeat_6
      delay: 0

# Repeat 6 lần nữa
truedmg_repeat_6:
  mechanics:
    # Lần 2
    second_iteration:
      type: script
      name: truedmg_single_iteration
      delay: 0

    # Lần 3
    third_iteration:
      type: script
      name: truedmg_single_iteration
      delay: 20

    # Lần 4
    fourth_iteration:
      type: script
      name: truedmg_single_iteration
      delay: 40

    # Lần 5
    fifth_iteration:
      type: script
      name: truedmg_single_iteration
      delay: 60

    # Lần 6
    sixth_iteration:
      type: script
      name: truedmg_single_iteration
      delay: 80

    # Lần 7
    seventh_iteration:
      type: script
      name: truedmg_single_iteration
      delay: 100

# Một lần iteration đơn lẻ
truedmg_single_iteration:
  mechanics:
    # Tạo particle dome
    dome_particles:
      type: particle
      particle: REDSTONE
      amount: 25
      x: 15
      y: 10
      z: 15
      data: 0.6,0,0.9 # Màu tím
      target:
        type: caster
        position: FEET

    # Particle ở giữa
    center_particles:
      type: particle
      particle: ENCHANTMENT_TABLE
      amount: 3
      target:
        type: caster
        position: BODY

    # Damage tất cả trong phạm vi
    area_damage:
      type: damage
      amount: 25
      damage_type: MAGIC,SKILL,DOT
      ignore_armor: true
      ignore_resistances: true
      target:
        type: nearby_entities
        radius: 15
        ignore_caster: false

    # Âm thanh damage
    damage_sound:
      type: sound
      sound: ENTITY_PLAYER_HURT
      volume: 0.8
      pitch: 0.8
      target:
        type: caster
