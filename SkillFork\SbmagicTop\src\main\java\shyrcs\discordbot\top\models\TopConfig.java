package shyrcs.discordbot.top.models;

import java.util.List;

public class TopConfig {

    private final String id;
    private final String name;
    private final String description;
    private final int color;
    private final String thumbnail;
    private final List<String> content;
    private final boolean webhookEnabled;
    private final String webhookUrl;
    private final int updateInterval;

    public TopConfig(String id, String name, String description, int color, String thumbnail, List<String> content) {
        this(id, name, description, color, thumbnail, content, true, "", 60);
    }

    public TopConfig(String id, String name, String description, int color, String thumbnail, List<String> content,
                    boolean webhookEnabled, String webhookUrl, int updateInterval) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.color = color;
        this.thumbnail = thumbnail;
        this.content = content;
        this.webhookEnabled = webhookEnabled;
        this.webhookUrl = webhookUrl;
        this.updateInterval = updateInterval;
    }
    
    public String getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public int getColor() {
        return color;
    }
    
    public String getThumbnail() {
        return thumbnail;
    }
    
    public List<String> getContent() {
        return content;
    }

    public boolean isWebhookEnabled() {
        return webhookEnabled;
    }

    public String getWebhookUrl() {
        return webhookUrl;
    }

    public int getUpdateInterval() {
        return updateInterval;
    }
}
