# SoulShopkeeper Plugin

SoulShopkeeper là một plugin Minecraft **độ<PERSON> lập** đ<PERSON><PERSON><PERSON> thiết kế để tạo và quản lý shopkeeper với hỗ trợ đầy đủ cho MMOItems và bảo toàn NBT data. Plugin hoạt động hoàn toàn độc lập, không cần dependency từ các plugin shopkeeper kh<PERSON><PERSON>.

## Tính năng chính

### 🔥 Hỗ trợ Đa dạng Items
- **MMOItems**: Bảo toàn đầy đủ NBT data, enchantments, và custom properties
- **Vanilla Items**: Hỗ trợ tất cả items vanilla của Minecraft
- **Flexible Storage**: <PERSON><PERSON> thống lưu trữ thông minh cho cả hai loại items
- **Auto-Detection**: Tự động phát hiện và xử lý đúng loại item

### 🤖 AI Shopkeeper Behavior
- **Smart Looking**: NPCs tự động nhìn theo người chơi gần nhất
- **Realistic Interaction**: Behavior giống NPC thật trong game
- **Configurable AI**: <PERSON><PERSON> thể bật/tắt AI behavior trong config

### 🎮 GUI Quản lý Trực quan
- GUI tạo shopkeeper với nhiều loại entity
- GUI chỉnh sửa trades với drag & drop
- Hỗ trợ preview items trước khi lưu
- Interface thân thiện với người dùng

### ⚡ Hiệu suất Cao
- **SQLite Database**: Lưu trữ nhanh và đáng tin cậy
- **Connection Pooling**: HikariCP cho hiệu suất tối ưu
- Cache system thông minh
- Async data saving
- Optimized cho server lớn

## Cài đặt

### Yêu cầu
- **Minecraft**: 1.21+
- **Server**: Paper/Spigot
- **Java**: 17+
- **Dependencies**: MMOItems (bắt buộc)
- **Optional**: MythicLib, MMOCore

### Bước cài đặt
1. Tải file `.jar` và đặt vào thư mục `plugins/`
2. Khởi động server
3. Chỉnh sửa `config.yml` theo nhu cầu
4. Restart server

## Sử dụng

### Commands cơ bản
```
/soulshopkeeper create          - Tạo shopkeeper mới
/soulshopkeeper edit [id]       - Chỉnh sửa shopkeeper
/soulshopkeeper list            - Xem danh sách shopkeeper của bạn
/soulshopkeeper delete <id>     - Xóa shopkeeper
/soulshopkeeper info <id>       - Xem thông tin shopkeeper
/soulshopkeeper reload          - Reload config
/soulshopkeeper save            - Lưu dữ liệu
/soulshopkeeper test            - Test database storage
/soulshopkeeper stats           - Xem thống kê database
/soulshopkeeper maintenance     - Bảo trì database
```

### Tạo Shopkeeper
1. Chạy `/soulshopkeeper create`
2. Chọn loại entity (Villager, Zombie, Skeleton, v.v.)
3. Shopkeeper sẽ được tạo tại vị trí của bạn
4. GUI chỉnh sửa sẽ tự động mở

### Chỉnh sửa Trades
1. Mở GUI chỉnh sửa shopkeeper
2. Click vào slot trade trống hoặc có sẵn
3. Đặt items vào slots:
   - **Ingredient 1**: Item đầu tiên khách hàng cần
   - **Ingredient 2**: Item thứ hai (tùy chọn)
   - **Result**: Item khách hàng nhận được
4. Click "Save Trade"

### Bảo toàn MMOItems
Plugin tự động:
- Lưu trữ tất cả NBT data của MMOItems
- Khôi phục items với đầy đủ thuộc tính
- Cache items để tăng hiệu suất
- Validate items sau khi server restart

## Cấu hình

### config.yml chính
```yaml
# Cài đặt chung
general:
  language: "en"
  debug: false
  auto-save-interval: 5
  cleanup-interval: 24

# Lưu trữ dữ liệu
storage:
  # Loại storage: SQLITE (khuyến nghị), MYSQL, hoặc YAML
  type: "SQLITE"

  # Cài đặt SQLite
  sqlite:
    file-name: "soulshopkeeper.db"
    wal-mode: true
    pool-size: 10
    timeout: 30

# Tích hợp MMOItems
mmoitems:
  enabled: true
  preserve-nbt: true
  cache-items: true
  cache-duration: 30

# Cài đặt shopkeeper
shopkeeper:
  default-type: "VILLAGER"
  max-per-player: -1              # -1 = unlimited, >0 = specific limit
  max-trades-per-shopkeeper: -1   # -1 = unlimited, >0 = specific limit
  allow-player-creation: true     # Allow players to create shopkeepers
  require-permission: true        # Require permission to create shopkeepers
```

### Permissions
```yaml
soulshopkeeper.admin      # Quyền admin đầy đủ
soulshopkeeper.create     # Tạo shopkeeper (required if require-permission: true)
soulshopkeeper.edit       # Chỉnh sửa shopkeeper
soulshopkeeper.delete     # Xóa shopkeeper
soulshopkeeper.use        # Sử dụng shopkeeper
```

**Lưu ý**: Nếu `require-permission: false` trong config, mọi người đều có thể tạo shopkeeper mà không cần permission.

## API cho Developers

### MMOItemStorage API
```java
// Lưu trữ MMOItem với NBT preservation
String itemId = plugin.getMMOItemStorage().storeMMOItem(itemStack);

// Khôi phục MMOItem với đầy đủ NBT data
ItemStack restoredItem = plugin.getMMOItemStorage().retrieveMMOItem(itemId);

// Kiểm tra xem item có phải MMOItem không
boolean isMMO = plugin.getMMOItemStorage().isMMOItem(itemStack);
```

### Shopkeeper Management API
```java
// Tạo shopkeeper mới
SoulShopkeeper shopkeeper = new SoulShopkeeper(id, name, type, location, ownerId);
plugin.getDataManager().addShopkeeper(shopkeeper);

// Thêm trade
ShopkeeperTrade trade = new ShopkeeperTrade(resultId, ingredient1Id, ingredient2Id);
shopkeeper.addTrade(trade);
```

## Kiến trúc Plugin

### Core Classes
- **`SoulShopkeeperPlugin`**: Main plugin class
- **`MMOItemStorage`**: Hệ thống lưu trữ MMOItems với NBT preservation
- **`ShopkeeperDataManager`**: Quản lý dữ liệu shopkeeper
- **`ShopkeeperGUIManager`**: Quản lý GUI interfaces

### Data Classes
- **`SoulShopkeeper`**: Đại diện cho một shopkeeper
- **`ShopkeeperTrade`**: Đại diện cho một trade
- **`StoredMMOItem`**: Lưu trữ MMOItem data

### Utilities
- **`MMOItemUtils`**: Tiện ích làm việc với MMOItems
- **`MessageUtils`**: Xử lý messages và colors

## Tính năng Nâng cao

### NBT Preservation System
```java
// Hệ thống này đảm bảo:
// 1. Lưu trữ toàn bộ NBT data của MMOItems
// 2. Khôi phục chính xác sau server restart
// 3. Cache để tăng hiệu suất
// 4. Validate tính hợp lệ của items
```

### Cache System
- **Item Cache**: Cache ItemStacks để truy cập nhanh
- **Data Cache**: Cache shopkeeper data
- **Auto Cleanup**: Tự động dọn dẹp cache cũ

### Performance Optimization
- **SQLite Database**: Fast and reliable storage
- **HikariCP**: Professional connection pooling
- **WAL Mode**: Write-Ahead Logging for better performance
- Async data saving
- Batch operations
- Memory-efficient storage
- Optimized for large servers

## Troubleshooting

### Vấn đề thường gặp

**Q: MMOItems bị mất thuộc tính sau khi restart server?**
A: Đảm bảo `preserve-nbt: true` trong config và MMOItems plugin được load trước SoulShopkeeper.

**Q: GUI không mở được?**
A: Kiểm tra permissions và đảm bảo player có quyền `soulshopkeeper.use`.

**Q: Shopkeeper không spawn?**
A: Kiểm tra location có hợp lệ và không bị block bởi plugin khác.

### Debug Mode
Bật debug trong config để xem thông tin chi tiết:
```yaml
general:
  debug: true
```

## Hỗ trợ

- **GitHub**: [Repository Link]
- **Discord**: [Discord Server]
- **Issues**: Báo cáo bug qua GitHub Issues

## License

MIT License - Xem file LICENSE để biết chi tiết.

---

**Phát triển bởi**: shyrcs  
**Version**: 1.0.0  
**Tương thích**: Minecraft 1.21+
