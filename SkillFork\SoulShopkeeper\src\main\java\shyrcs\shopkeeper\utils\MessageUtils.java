package shyrcs.shopkeeper.utils;

import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Utility class for handling messages and color codes
 */
public class MessageUtils {
    
    /**
     * Sends a colored message to a command sender
     * @param sender The command sender
     * @param message The message with color codes
     */
    public static void sendMessage(CommandSender sender, String message) {
        if (sender != null && message != null) {
            sender.sendMessage(colorize(message));
        }
    }
    
    /**
     * Sends multiple colored messages to a command sender
     * @param sender The command sender
     * @param messages The messages with color codes
     */
    public static void sendMessages(CommandSender sender, String... messages) {
        if (sender != null && messages != null) {
            for (String message : messages) {
                if (message != null) {
                    sender.sendMessage(colorize(message));
                }
            }
        }
    }
    
    /**
     * Colorizes a string by replacing color codes
     * @param text The text to colorize
     * @return The colorized text
     */
    public static String colorize(String text) {
        if (text == null) {
            return null;
        }
        return ChatColor.translateAlternateColorCodes('&', text);
    }
    
    /**
     * Strips color codes from a string
     * @param text The text to strip colors from
     * @return The text without color codes
     */
    public static String stripColors(String text) {
        if (text == null) {
            return null;
        }
        return ChatColor.stripColor(colorize(text));
    }
    
    /**
     * Sends a title to a player
     * @param player The player
     * @param title The title text
     * @param subtitle The subtitle text
     */
    public static void sendTitle(Player player, String title, String subtitle) {
        if (player != null) {
            player.sendTitle(
                colorize(title != null ? title : ""),
                colorize(subtitle != null ? subtitle : ""),
                10, 70, 20
            );
        }
    }
    
    /**
     * Sends a title to a player with custom timing
     * @param player The player
     * @param title The title text
     * @param subtitle The subtitle text
     * @param fadeIn Fade in time in ticks
     * @param stay Stay time in ticks
     * @param fadeOut Fade out time in ticks
     */
    public static void sendTitle(Player player, String title, String subtitle, int fadeIn, int stay, int fadeOut) {
        if (player != null) {
            player.sendTitle(
                colorize(title != null ? title : ""),
                colorize(subtitle != null ? subtitle : ""),
                fadeIn, stay, fadeOut
            );
        }
    }
    
    /**
     * Sends an action bar message to a player
     * @param player The player
     * @param message The message
     */
    public static void sendActionBar(Player player, String message) {
        if (player != null && message != null) {
            // For newer versions, you might want to use the Adventure API
            // For now, using the legacy method
            try {
                player.spigot().sendMessage(net.md_5.bungee.api.ChatMessageType.ACTION_BAR, 
                    new net.md_5.bungee.api.chat.TextComponent(colorize(message)));
            } catch (Exception e) {
                // Fallback to regular message if action bar fails
                sendMessage(player, message);
            }
        }
    }
}
