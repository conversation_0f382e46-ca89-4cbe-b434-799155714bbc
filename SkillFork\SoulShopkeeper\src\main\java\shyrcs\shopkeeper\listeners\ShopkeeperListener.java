package shyrcs.shopkeeper.listeners;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityPushedByEntityEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.player.PlayerInteractEntityEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.Material;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.gui.ShopkeeperGUIManager;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

import java.util.UUID;

/**
 * Handles events related to shopkeepers and GUIs
 */
public class ShopkeeperListener implements Listener {
    
    private final SoulShopkeeperPlugin plugin;
    
    public ShopkeeperListener(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();

        // Check if this is a SoulShopkeeper GUI
        String title = event.getView().getTitle();
        if (!title.contains("Create Shopkeeper") &&
            !title.contains("Edit:") &&
            !title.contains("Trade Editor")) {
            return;
        }

        ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
        if (session == null) {
            event.setCancelled(true);
            return;
        }

        // Handle different click scenarios
        if (event.getClickedInventory() == null) {
            event.setCancelled(true);
            return;
        }

        // Check if clicking in player inventory vs GUI inventory
        boolean clickingInGUI = event.getClickedInventory().equals(event.getView().getTopInventory());

        try {
            switch (session.getType()) {
                case CREATION:
                    event.setCancelled(true);
                    if (clickingInGUI) {
                        handleCreationGUIClick(player, event, session);
                    }
                    break;
                case EDIT:
                    event.setCancelled(true);
                    if (clickingInGUI) {
                        handleEditGUIClick(player, event, session);
                    }
                    break;
                case TRADE_EDIT:
                    handleTradeEditGUIClick(player, event, session, clickingInGUI);
                    break;
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Error handling GUI click: " + e.getMessage());
            e.printStackTrace();
            player.closeInventory();
        }
    }
    
    /**
     * Handles clicks in the creation GUI
     */
    private void handleCreationGUIClick(Player player, InventoryClickEvent event, ShopkeeperGUIManager.GUISession session) {
        int slot = event.getSlot();
        
        switch (slot) {
            case 13: // Villager shopkeeper
                createShopkeeper(player, "VILLAGER");
                break;
            case 21: // Zombie shopkeeper
                createShopkeeper(player, "ZOMBIE");
                break;
            case 23: // Skeleton shopkeeper
                createShopkeeper(player, "SKELETON");
                break;
            case 49: // Cancel
                player.closeInventory();
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + 
                                       "&cShopkeeper creation cancelled.");
                break;
        }
    }
    
    /**
     * Handles clicks in the edit GUI
     */
    private void handleEditGUIClick(Player player, InventoryClickEvent event, ShopkeeperGUIManager.GUISession session) {
        int slot = event.getSlot();
        SoulShopkeeper shopkeeper = session.getShopkeeper();
        
        if (shopkeeper == null) {
            player.closeInventory();
            return;
        }
        
        // Trade slots - check if it's a valid trade slot
        if (isTradeSlot(slot)) {
            int tradeIndex = getTradeIndex(slot);

            if (event.isLeftClick()) {
                // Edit trade - either existing or new
                plugin.getGUIManager().openTradeEditGUI(player, shopkeeper, tradeIndex);
            } else if (event.isRightClick() && tradeIndex < shopkeeper.getTrades().size()) {
                // Toggle trade enabled (only for existing trades)
                ShopkeeperTrade trade = shopkeeper.getTrades().get(tradeIndex);
                trade.setEnabled(!trade.isEnabled());

                // Save changes immediately
                if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                    shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                        (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                    wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
                }

                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&aTrade " + (tradeIndex + 1) + " " +
                                       (trade.isEnabled() ? "enabled" : "disabled"));

                // Refresh GUI
                plugin.getGUIManager().openEditGUI(player, shopkeeper);
            }
            return;
        }
        
        switch (slot) {
            case 45: // Toggle active
                shopkeeper.setActive(!shopkeeper.isActive());

                // Save changes to database immediately
                if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                    shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                        (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                    wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
                } else {
                    plugin.getDataManager().saveAll();
                }

                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&aShopkeeper " + (shopkeeper.isActive() ? "activated" : "deactivated"));

                // Refresh entity
                plugin.getEntityManager().refreshShopkeeperEntity(shopkeeper);

                // Refresh GUI
                plugin.getGUIManager().openEditGUI(player, shopkeeper);
                break;
                
            case 49: // Close
                player.closeInventory();
                break;
                
            case 53: // Delete shopkeeper
                if (event.isShiftClick()) {
                    try {
                        // Remove entity first
                        plugin.getEntityManager().removeShopkeeperEntity(shopkeeper.getId());

                        // Remove from data
                        boolean deleted = false;
                        if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                            shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                                (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                            deleted = wrapper.getSQLiteDataManager().deleteShopkeeper(shopkeeper.getId());
                        } else {
                            plugin.getDataManager().removeShopkeeper(shopkeeper.getId());
                            deleted = true;
                        }

                        player.closeInventory();

                        if (deleted) {
                            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                                   plugin.getConfig().getString("messages.shopkeeper-deleted"));
                        } else {
                            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                                   "&cFailed to delete shopkeeper from database!");
                        }
                    } catch (Exception e) {
                        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                               "&cError deleting shopkeeper: " + e.getMessage());
                        plugin.getLogger().severe("Failed to delete shopkeeper " + shopkeeper.getId() + ": " + e.getMessage());
                        e.printStackTrace();
                    }
                } else {
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                           "&cShift-click to confirm deletion!");
                }
                break;
        }
    }
    
    /**
     * Handles clicks in the trade edit GUI
     */
    private void handleTradeEditGUIClick(Player player, InventoryClickEvent event, ShopkeeperGUIManager.GUISession session, boolean clickingInGUI) {
        int slot = event.getSlot();
        SoulShopkeeper shopkeeper = session.getShopkeeper();

        if (shopkeeper == null) {
            player.closeInventory();
            return;
        }

        // Handle trade item slots (allow item movement)
        if (clickingInGUI && (slot == 10 || slot == 12 || slot == 16)) {
            // Don't cancel event for trade slots - allow normal item interaction
            return;
        }

        // Cancel event for all other GUI interactions
        event.setCancelled(true);

        if (!clickingInGUI) {
            return; // Don't handle clicks in player inventory
        }

        switch (slot) {
            case 22: // Save trade
                saveTrade(player, event.getInventory(), shopkeeper, session.getTradeIndex());
                break;

            case 24: // Back/Cancel
                plugin.getGUIManager().openEditGUI(player, shopkeeper);
                break;

            case 26: // Delete trade
                if (session.getTradeIndex() >= 0 && session.getTradeIndex() < shopkeeper.getTrades().size()) {
                    shopkeeper.removeTrade(session.getTradeIndex());

                    // Save changes immediately
                    if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                        shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                            (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                        wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
                    }

                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                           "&aTrade deleted!");
                    plugin.getGUIManager().openEditGUI(player, shopkeeper);
                }
                break;

            default:
                // Ignore clicks on glass panes and other items
                break;
        }
    }
    
    /**
     * Creates a new shopkeeper
     */
    private void createShopkeeper(Player player, String type) {
        // Check if player creation is allowed
        if (!plugin.getConfig().getBoolean("shopkeeper.allow-player-creation", true)) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cPlayer shopkeeper creation is disabled!");
            return;
        }

        // Check permission if required
        if (plugin.getConfig().getBoolean("shopkeeper.require-permission", true)) {
            if (!player.hasPermission("soulshopkeeper.create")) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       plugin.getConfig().getString("messages.no-permission"));
                return;
            }
        }

        // Check shopkeeper limits
        int maxShopkeepers = plugin.getConfig().getInt("shopkeeper.max-per-player", -1);
        if (maxShopkeepers > 0) { // Only check limit if it's not unlimited (-1)
            int currentCount = plugin.getDataManager().getPlayerShopkeeperCount(player.getUniqueId());

            if (currentCount >= maxShopkeepers) {
                String message = plugin.getConfig().getString("messages.max-shopkeepers-reached",
                               "&cYou have reached the maximum number of shopkeepers!");
                message = message.replace("{limit}", String.valueOf(maxShopkeepers));
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + message);
                return;
            }
        }
        
        // Create shopkeeper
        UUID id = UUID.randomUUID();
        String name = player.getName() + "'s Shopkeeper";
        SoulShopkeeper shopkeeper = new SoulShopkeeper(id, name, type, player.getLocation(), player.getUniqueId());
        
        // Add to data manager
        plugin.getDataManager().addShopkeeper(shopkeeper);

        // Spawn the entity
        plugin.getEntityManager().spawnShopkeeperEntity(shopkeeper);

        player.closeInventory();
        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                               plugin.getConfig().getString("messages.shopkeeper-created"));

        // Open edit GUI immediately
        plugin.getGUIManager().openEditGUI(player, shopkeeper);
    }
    
    /**
     * Saves a trade from the trade edit GUI
     */
    private void saveTrade(Player player, org.bukkit.inventory.Inventory gui, SoulShopkeeper shopkeeper, int tradeIndex) {
        ItemStack ingredient1 = gui.getItem(10);
        ItemStack ingredient2 = gui.getItem(12);
        ItemStack result = gui.getItem(16);

        // Validate trade - check for actual items (not air or null)
        if (result == null || result.getType().isAir()) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cResult item is required!");
            return;
        }

        if (ingredient1 == null || ingredient1.getType().isAir()) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cFirst ingredient is required!");
            return;
        }

        // Store items using MMOItemStorage (handles both MMOItems and vanilla items)
        String resultId = plugin.getMMOItemStorage().storeMMOItem(result);
        String ingredient1Id = plugin.getMMOItemStorage().storeMMOItem(ingredient1);
        String ingredient2Id = null;

        if (ingredient2 != null && !ingredient2.getType().isAir()) {
            ingredient2Id = plugin.getMMOItemStorage().storeMMOItem(ingredient2);
        }

        if (resultId == null || ingredient1Id == null) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cFailed to store items! Please try again.");
            return;
        }

        // Create or update trade
        ShopkeeperTrade trade = new ShopkeeperTrade(resultId, ingredient1Id, ingredient2Id, -1, 0, true);

        try {
            if (tradeIndex >= 0 && tradeIndex < shopkeeper.getTrades().size()) {
                // Update existing trade
                shopkeeper.removeTrade(tradeIndex);
                shopkeeper.getTrades().add(tradeIndex, trade);
            } else {
                // Add new trade
                shopkeeper.addTrade(trade);
            }

            // Save changes immediately
            if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                    (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
            }

            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&aTrade saved successfully!");

            // Return to edit GUI
            plugin.getGUIManager().openEditGUI(player, shopkeeper);

        } catch (Exception e) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cFailed to save trade: " + e.getMessage());
            plugin.getLogger().severe("Failed to save trade: " + e.getMessage());
            e.printStackTrace();
        }
    }


    
    /**
     * Checks if a slot is a trade slot
     */
    private boolean isTradeSlot(int slot) {
        // Trade slots: 10-16, 19-25, 28-34 (excluding borders)
        return (slot >= 10 && slot <= 16 && slot != 17 && slot != 18) ||
               (slot >= 19 && slot <= 25 && slot != 26 && slot != 27) ||
               (slot >= 28 && slot <= 34 && slot != 35 && slot != 36);
    }

    /**
     * Gets the trade index from a slot
     */
    private int getTradeIndex(int slot) {
        if (slot >= 10 && slot <= 16) {
            return slot - 10;
        } else if (slot >= 19 && slot <= 25) {
            return (slot - 19) + 7;
        } else if (slot >= 28 && slot <= 34) {
            return (slot - 28) + 14;
        }
        return -1;
    }
    
    @EventHandler
    public void onInventoryDrag(org.bukkit.event.inventory.InventoryDragEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();

        // Check if this is a SoulShopkeeper GUI
        String title = event.getView().getTitle();
        if (title.contains("Create Shopkeeper") || title.contains("Edit:")) {
            // Cancel drag events in creation and edit GUIs
            event.setCancelled(true);
        } else if (title.contains("Trade Editor")) {
            // Allow drag only to trade slots (10, 12, 16)
            boolean allowDrag = true;
            for (int slot : event.getRawSlots()) {
                if (slot != 10 && slot != 12 && slot != 16) {
                    allowDrag = false;
                    break;
                }
            }

            if (!allowDrag) {
                event.setCancelled(true);
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // Check if the damaged entity is a shopkeeper
        if (plugin.getEntityManager().isShopkeeperEntity(event.getEntity())) {
            // Shopkeepers are always immortal - cancel ALL damage
            event.setCancelled(true);

            // Show message only to players who try to damage
            if (event.getDamager() instanceof Player) {
                Player player = (Player) event.getDamager();
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cShopkeepers are immortal and cannot be damaged!");
            }

            // Ensure health stays at maximum
            if (event.getEntity() instanceof org.bukkit.entity.LivingEntity) {
                org.bukkit.entity.LivingEntity living = (org.bukkit.entity.LivingEntity) event.getEntity();
                living.setHealth(living.getMaxHealth());
                living.setFireTicks(0);
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamage(org.bukkit.event.entity.EntityDamageEvent event) {
        // Check if the damaged entity is a shopkeeper
        if (plugin.getEntityManager().isShopkeeperEntity(event.getEntity())) {
            // Shopkeepers are always immortal - cancel ALL damage from any source
            event.setCancelled(true);

            // Ensure health stays at maximum
            if (event.getEntity() instanceof org.bukkit.entity.LivingEntity) {
                org.bukkit.entity.LivingEntity living = (org.bukkit.entity.LivingEntity) event.getEntity();
                living.setHealth(living.getMaxHealth());
                living.setFireTicks(0);
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityPushedByEntity(EntityPushedByEntityEvent event) {
        // Check if the pushed entity is a shopkeeper
        if (plugin.getEntityManager().isShopkeeperEntity(event.getEntity())) {
            // Cancel push events - shopkeepers cannot be moved
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getPlayer() instanceof Player) {
            Player player = (Player) event.getPlayer();

            // Clean up GUI session after a short delay
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                plugin.getGUIManager().removeSession(player.getUniqueId());
            }, 1L);
        }
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // Clean up GUI session when player quits
        plugin.getGUIManager().removeSession(event.getPlayer().getUniqueId());
    }
    
    @EventHandler
    public void onPlayerInteractEntity(PlayerInteractEntityEvent event) {
        // Handle shopkeeper entity interactions
        Player player = event.getPlayer();
        org.bukkit.entity.Entity entity = event.getRightClicked();

        // Check if the entity is a shopkeeper using entity manager
        if (plugin.getEntityManager().isShopkeeperEntity(entity)) {
            event.setCancelled(true);

            SoulShopkeeper shopkeeper = plugin.getEntityManager().getShopkeeper(entity);
            if (shopkeeper != null) {
                if (player.isSneaking() && shopkeeper.isOwnedBy(player.getUniqueId())) {
                    // Owner shift-clicking opens edit GUI
                    plugin.getGUIManager().openEditGUI(player, shopkeeper);
                } else {
                    // Regular interaction opens trading interface
                    openTradingGUI(player, shopkeeper);
                }
            }
        }
    }

    /**
     * Opens the trading GUI for a shopkeeper
     */
    private void openTradingGUI(Player player, SoulShopkeeper shopkeeper) {
        // For now, just show a message. Later this would open the actual trading GUI
        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                               "&aOpening " + shopkeeper.getName() + "'s shop...");

        // Show available trades
        if (shopkeeper.hasTrades()) {
            MessageUtils.sendMessage(player, "&7Available trades: " + shopkeeper.getEnabledTradeCount());

            // List first few trades as example
            int count = 0;
            for (ShopkeeperTrade trade : shopkeeper.getEnabledTrades()) {
                if (count >= 3) break; // Show max 3 trades in chat

                ItemStack result = trade.getResultItem();
                ItemStack ingredient1 = trade.getIngredient1Item();

                if (result != null && ingredient1 != null) {
                    String resultName = getItemDisplayName(result);
                    String ingredient1Name = getItemDisplayName(ingredient1);

                    MessageUtils.sendMessage(player, "&7- " + ingredient1Name + " → " + resultName);
                }
                count++;
            }

            if (shopkeeper.getEnabledTradeCount() > 3) {
                MessageUtils.sendMessage(player, "&7... and " + (shopkeeper.getEnabledTradeCount() - 3) + " more trades");
            }
        } else {
            MessageUtils.sendMessage(player, "&cThis shopkeeper has no available trades!");
        }
    }



    /**
     * Gets display name for an item
     */
    private String getItemDisplayName(ItemStack item) {
        if (item == null) return "Unknown";

        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return MessageUtils.stripColors(item.getItemMeta().getDisplayName());
        }

        // Check if it's an MMOItem
        String mmoInfo = plugin.getMMOItemStorage().getMMOItemInfo(item);
        if (mmoInfo != null) {
            return mmoInfo;
        }

        return item.getType().name().toLowerCase().replace('_', ' ');
    }
}
