package shyrcs.shopkeeper.listeners;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityTeleportEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.player.PlayerInteractEntityEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.Material;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.gui.ShopkeeperGUIManager;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

import java.util.UUID;

/**
 * Handles events related to shopkeepers and GUIs
 */
public class ShopkeeperListener implements Listener {
    
    private final SoulShopkeeperPlugin plugin;
    
    public ShopkeeperListener(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();

        // Check if this is a SoulShopkeeper GUI (excluding Trade Editor - handled separately)
        String title = event.getView().getTitle();
        if (!title.contains("Create Shopkeeper") &&
            !title.contains("Edit:")) {
            return;
        }

        ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
        if (session == null) {
            event.setCancelled(true);
            return;
        }

        // Handle different click scenarios
        if (event.getClickedInventory() == null) {
            event.setCancelled(true);
            return;
        }

        try {
            switch (session.getType()) {
                case CREATION:
                    event.setCancelled(true);
                    if (event.getClickedInventory().equals(event.getView().getTopInventory())) {
                        handleCreationGUIClick(player, event, session);
                    }
                    break;
                case EDIT:
                    event.setCancelled(true);
                    if (event.getClickedInventory().equals(event.getView().getTopInventory())) {
                        handleEditGUIClick(player, event, session);
                    }
                    break;
                // TRADE_EDIT is now handled by TradeEditGUIListener
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Error handling GUI click: " + e.getMessage());
            e.printStackTrace();
            player.closeInventory();
        }
    }
    
    /**
     * Handles clicks in the creation GUI
     */
    private void handleCreationGUIClick(Player player, InventoryClickEvent event, ShopkeeperGUIManager.GUISession session) {
        int slot = event.getSlot();
        
        switch (slot) {
            case 13: // Villager shopkeeper
                createShopkeeper(player, "VILLAGER");
                break;
            case 21: // Zombie shopkeeper
                createShopkeeper(player, "ZOMBIE");
                break;
            case 23: // Skeleton shopkeeper
                createShopkeeper(player, "SKELETON");
                break;
            case 49: // Cancel
                player.closeInventory();
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + 
                                       "&cShopkeeper creation cancelled.");
                break;
        }
    }
    
    /**
     * Handles clicks in the edit GUI
     */
    private void handleEditGUIClick(Player player, InventoryClickEvent event, ShopkeeperGUIManager.GUISession session) {
        int slot = event.getSlot();
        SoulShopkeeper shopkeeper = session.getShopkeeper();
        
        if (shopkeeper == null) {
            player.closeInventory();
            return;
        }
        
        // Trade slots - check if it's a valid trade slot
        if (isTradeSlot(slot)) {
            int tradeIndex = getTradeIndex(slot);

            if (event.isLeftClick()) {
                // Edit trade - either existing or new
                plugin.getGUIManager().openTradeEditGUI(player, shopkeeper, tradeIndex);
            } else if (event.isRightClick() && tradeIndex < shopkeeper.getTrades().size()) {
                // Toggle trade enabled (only for existing trades)
                ShopkeeperTrade trade = shopkeeper.getTrades().get(tradeIndex);
                trade.setEnabled(!trade.isEnabled());

                // Save changes immediately
                if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                    shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                        (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                    wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
                }

                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&aTrade " + (tradeIndex + 1) + " " +
                                       (trade.isEnabled() ? "enabled" : "disabled"));

                // Refresh GUI
                plugin.getGUIManager().openEditGUI(player, shopkeeper);
            }
            return;
        }
        
        switch (slot) {
            case 45: // Toggle active
                shopkeeper.setActive(!shopkeeper.isActive());

                // Save changes to database immediately
                if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                    shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                        (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                    wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
                } else {
                    plugin.getDataManager().saveAll();
                }

                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&aShopkeeper " + (shopkeeper.isActive() ? "activated" : "deactivated"));

                // Refresh entity
                plugin.getEntityManager().refreshShopkeeperEntity(shopkeeper);

                // Refresh GUI
                plugin.getGUIManager().openEditGUI(player, shopkeeper);
                break;
                
            case 49: // Close
                player.closeInventory();
                break;
                
            case 53: // Delete shopkeeper
                if (event.isShiftClick()) {
                    try {
                        // Remove entity first
                        plugin.getEntityManager().removeShopkeeperEntity(shopkeeper.getId());

                        // Remove from data
                        boolean deleted = false;
                        if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                            shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                                (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                            deleted = wrapper.getSQLiteDataManager().deleteShopkeeper(shopkeeper.getId());
                        } else {
                            plugin.getDataManager().removeShopkeeper(shopkeeper.getId());
                            deleted = true;
                        }

                        player.closeInventory();

                        if (deleted) {
                            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                                   plugin.getConfig().getString("messages.shopkeeper-deleted"));
                        } else {
                            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                                   "&cFailed to delete shopkeeper from database!");
                        }
                    } catch (Exception e) {
                        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                               "&cError deleting shopkeeper: " + e.getMessage());
                        plugin.getLogger().severe("Failed to delete shopkeeper " + shopkeeper.getId() + ": " + e.getMessage());
                        e.printStackTrace();
                    }
                } else {
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                           "&cShift-click to confirm deletion!");
                }
                break;
        }
    }
    
    // Trade edit GUI handling moved to TradeEditGUIListener
    
    /**
     * Creates a new shopkeeper
     */
    private void createShopkeeper(Player player, String type) {
        // Check if player creation is allowed
        if (!plugin.getConfig().getBoolean("shopkeeper.allow-player-creation", true)) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cPlayer shopkeeper creation is disabled!");
            return;
        }

        // Check permission if required
        if (plugin.getConfig().getBoolean("shopkeeper.require-permission", true)) {
            if (!player.hasPermission("soulshopkeeper.create")) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       plugin.getConfig().getString("messages.no-permission"));
                return;
            }
        }

        // Check shopkeeper limits
        int maxShopkeepers = plugin.getConfig().getInt("shopkeeper.max-per-player", -1);
        if (maxShopkeepers > 0) { // Only check limit if it's not unlimited (-1)
            int currentCount = plugin.getDataManager().getPlayerShopkeeperCount(player.getUniqueId());

            if (currentCount >= maxShopkeepers) {
                String message = plugin.getConfig().getString("messages.max-shopkeepers-reached",
                               "&cYou have reached the maximum number of shopkeepers!");
                message = message.replace("{limit}", String.valueOf(maxShopkeepers));
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + message);
                return;
            }
        }
        
        // Create shopkeeper
        UUID id = UUID.randomUUID();
        String name = player.getName() + "'s Shopkeeper";
        SoulShopkeeper shopkeeper = new SoulShopkeeper(id, name, type, player.getLocation(), player.getUniqueId());
        
        // Add to data manager
        plugin.getDataManager().addShopkeeper(shopkeeper);

        // Spawn the entity
        plugin.getEntityManager().spawnShopkeeperEntity(shopkeeper);

        player.closeInventory();
        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                               plugin.getConfig().getString("messages.shopkeeper-created"));

        // Open edit GUI immediately
        plugin.getGUIManager().openEditGUI(player, shopkeeper);
    }
    
    // saveTrade method moved to TradeEditGUIListener


    
    /**
     * Checks if a slot is a trade slot
     */
    private boolean isTradeSlot(int slot) {
        // Trade slots: 10-16, 19-25, 28-34 (excluding borders)
        return (slot >= 10 && slot <= 16 && slot != 17 && slot != 18) ||
               (slot >= 19 && slot <= 25 && slot != 26 && slot != 27) ||
               (slot >= 28 && slot <= 34 && slot != 35 && slot != 36);
    }

    /**
     * Gets the trade index from a slot
     */
    private int getTradeIndex(int slot) {
        if (slot >= 10 && slot <= 16) {
            return slot - 10;
        } else if (slot >= 19 && slot <= 25) {
            return (slot - 19) + 7;
        } else if (slot >= 28 && slot <= 34) {
            return (slot - 28) + 14;
        }
        return -1;
    }
    
    @EventHandler
    public void onInventoryDrag(org.bukkit.event.inventory.InventoryDragEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();

        // Check if this is a SoulShopkeeper GUI (excluding Trade Editor - handled separately)
        String title = event.getView().getTitle();
        if (title.contains("Create Shopkeeper") || title.contains("Edit:")) {
            // Cancel drag events in creation and edit GUIs
            event.setCancelled(true);
        }
        // Trade Editor drag events handled by TradeEditGUIListener
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // Check if the damaged entity is a shopkeeper
        if (plugin.getEntityManager().isShopkeeperEntity(event.getEntity())) {
            // Shopkeepers are always immortal - cancel ALL damage
            event.setCancelled(true);

            // Show message only to players who try to damage
            if (event.getDamager() instanceof Player) {
                Player player = (Player) event.getDamager();
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cShopkeepers are immortal and cannot be damaged!");
            }

            // Ensure health stays at maximum
            if (event.getEntity() instanceof org.bukkit.entity.LivingEntity) {
                org.bukkit.entity.LivingEntity living = (org.bukkit.entity.LivingEntity) event.getEntity();
                living.setHealth(living.getMaxHealth());
                living.setFireTicks(0);
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamage(org.bukkit.event.entity.EntityDamageEvent event) {
        // Check if the damaged entity is a shopkeeper
        if (plugin.getEntityManager().isShopkeeperEntity(event.getEntity())) {
            // Shopkeepers are always immortal - cancel ALL damage from any source
            event.setCancelled(true);

            // Ensure health stays at maximum
            if (event.getEntity() instanceof org.bukkit.entity.LivingEntity) {
                org.bukkit.entity.LivingEntity living = (org.bukkit.entity.LivingEntity) event.getEntity();
                living.setHealth(living.getMaxHealth());
                living.setFireTicks(0);
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityTeleport(EntityTeleportEvent event) {
        // Check if the teleporting entity is a shopkeeper
        if (plugin.getEntityManager().isShopkeeperEntity(event.getEntity())) {
            // Cancel all teleports except those we initiate for looking behavior
            // We'll handle position locking in the position monitor
            event.setCancelled(true);
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerInteractEntity(org.bukkit.event.player.PlayerInteractEntityEvent event) {
        // Check if the entity is a shopkeeper
        if (plugin.getEntityManager().isShopkeeperEntity(event.getRightClicked())) {
            // Handle shopkeeper interaction
            Player player = event.getPlayer();

            // Get shopkeeper ID from entity
            java.util.UUID shopkeeperId = plugin.getEntityManager().getShopkeeperIdFromEntity(event.getRightClicked());
            if (shopkeeperId != null) {
                SoulShopkeeper shopkeeper = plugin.getDataManager().getShopkeeper(shopkeeperId);
                if (shopkeeper != null) {
                    // Open shopkeeper GUI or handle interaction
                    plugin.getGUIManager().openEditGUI(player, shopkeeper);
                    event.setCancelled(true);
                }
            }
        }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getPlayer() instanceof Player) {
            Player player = (Player) event.getPlayer();

            // Clean up GUI session after a short delay
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                plugin.getGUIManager().removeSession(player.getUniqueId());
            }, 1L);
        }
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // Clean up GUI session when player quits
        plugin.getGUIManager().removeSession(event.getPlayer().getUniqueId());
    }
    


    /**
     * Opens the trading GUI for a shopkeeper
     */
    private void openTradingGUI(Player player, SoulShopkeeper shopkeeper) {
        // For now, just show a message. Later this would open the actual trading GUI
        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                               "&aOpening " + shopkeeper.getName() + "'s shop...");

        // Show available trades
        if (shopkeeper.hasTrades()) {
            MessageUtils.sendMessage(player, "&7Available trades: " + shopkeeper.getEnabledTradeCount());

            // List first few trades as example
            int count = 0;
            for (ShopkeeperTrade trade : shopkeeper.getEnabledTrades()) {
                if (count >= 3) break; // Show max 3 trades in chat

                ItemStack result = trade.getResultItem();
                ItemStack ingredient1 = trade.getIngredient1Item();

                if (result != null && ingredient1 != null) {
                    String resultName = getItemDisplayName(result);
                    String ingredient1Name = getItemDisplayName(ingredient1);

                    MessageUtils.sendMessage(player, "&7- " + ingredient1Name + " → " + resultName);
                }
                count++;
            }

            if (shopkeeper.getEnabledTradeCount() > 3) {
                MessageUtils.sendMessage(player, "&7... and " + (shopkeeper.getEnabledTradeCount() - 3) + " more trades");
            }
        } else {
            MessageUtils.sendMessage(player, "&cThis shopkeeper has no available trades!");
        }
    }



    /**
     * Gets display name for an item
     */
    private String getItemDisplayName(ItemStack item) {
        if (item == null) return "Unknown";

        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return MessageUtils.stripColors(item.getItemMeta().getDisplayName());
        }

        // Check if it's an MMOItem
        String mmoInfo = plugin.getMMOItemStorage().getMMOItemInfo(item);
        if (mmoInfo != null) {
            return mmoInfo;
        }

        return item.getType().name().toLowerCase().replace('_', ' ');
    }
}
