package shyrcs.Ability;

import io.lumine.mythic.lib.api.item.NBTItem;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Random;

/**
 * Listener để tăng drop rate cho MythicMob
 */
public class MythicDropBoostListener implements Listener {
    
    private final Plugin plugin;
    private final Random random = new Random();
    
    // Cache reflection objects để tối ưu performance
    private Class<?> mythicMobDeathEventClass;
    private Method getEntityMethod;
    private Method getDropsMethod;
    private Method getMobTypeMethod;
    private Method getKillerMethod;
    private Field dropAmountField;
    private boolean reflectionInitialized = false;
    
    public MythicDropBoostListener(Plugin plugin) {
        this.plugin = plugin;
        initializeReflection();
    }
    
    /**
     * Khởi tạo reflection để hook vào MythicMobs
     */
    private void initializeReflection() {
        try {
            // Tìm MythicMobDeathEvent class
            mythicMobDeathEventClass = Class.forName("io.lumine.mythic.bukkit.events.MythicMobDeathEvent");
            
            // Lấy các method cần thiết
            getEntityMethod = mythicMobDeathEventClass.getMethod("getEntity");
            getDropsMethod = mythicMobDeathEventClass.getMethod("getDrops");
            getMobTypeMethod = mythicMobDeathEventClass.getMethod("getMobType");
            getKillerMethod = mythicMobDeathEventClass.getMethod("getKiller");
            
            // Tìm Drop class và field amount
            Class<?> dropClass = Class.forName("io.lumine.mythic.core.drops.Drop");
            try {
                dropAmountField = dropClass.getDeclaredField("amount");
                dropAmountField.setAccessible(true);
            } catch (NoSuchFieldException e) {
                // Thử tìm field khác có thể chứa amount
                Field[] fields = dropClass.getDeclaredFields();
                for (Field field : fields) {
                    if (field.getName().toLowerCase().contains("amount") || 
                        field.getType().toString().contains("RandomDouble")) {
                        dropAmountField = field;
                        dropAmountField.setAccessible(true);
                        break;
                    }
                }
            }
            
            reflectionInitialized = true;
            plugin.getLogger().info("MythicDropBoostListener: Reflection initialized successfully");
            
        } catch (Exception e) {
            plugin.getLogger().warning("MythicDropBoostListener: Failed to initialize reflection - " + e.getMessage());
            reflectionInitialized = false;
        }
    }
    
    /**
     * Hook vào EntityDeathEvent để kiểm tra MythicMob
     */
    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    public void onEntityDeath(EntityDeathEvent event) {
        if (!reflectionInitialized) {
            return;
        }

        // Kiểm tra xem có phải MythicMob không thông qua reflection
        Entity entity = event.getEntity();
        if (!isMythicMob(entity)) {
            return;
        }

        // Xử lý MythicMob death
        handleMythicMobDeath(event);
    }

    /**
     * Kiểm tra xem entity có phải MythicMob không
     */
    private boolean isMythicMob(Entity entity) {
        try {
            // Kiểm tra xem có MythicMobs plugin không
            if (plugin.getServer().getPluginManager().getPlugin("MythicMobs") == null) {
                return false;
            }

            // Sử dụng reflection để kiểm tra
            Class<?> mythicMobsClass = Class.forName("io.lumine.mythic.bukkit.MythicBukkit");
            Object mythicMobs = mythicMobsClass.getMethod("inst").invoke(null);
            Object mobManager = mythicMobsClass.getMethod("getMobManager").invoke(mythicMobs);

            Class<?> mobManagerClass = Class.forName("io.lumine.mythic.core.mobs.MobManager");
            Object mythicMob = mobManagerClass.getMethod("getMythicMobInstance", Entity.class)
                .invoke(mobManager, entity);

            return mythicMob != null;

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Xử lý MythicMob death
     */
    private void handleMythicMobDeath(EntityDeathEvent event) {
        try {
            Entity entity = event.getEntity();
            Player killer = event.getEntity().getKiller();

            if (killer == null) return;

            // get drop rate% event
            double dropBoostValue = getDropBoostValue(killer);
            if (dropBoostValue <= 0) return;

            // get drop event - sử dụng drops từ EntityDeathEvent
            if (event.getDrops() == null || event.getDrops().isEmpty()) return;

            // aply boost
            applyDropBoost(event.getDrops(), dropBoostValue, killer, entity);

        } catch (Exception e) {
            plugin.getLogger().warning("Error in MythicDropBoostListener: " + e.getMessage());
        }
    }
    
    /**
     * Lấy player killer từ event
     */
    private Player getKillerPlayer(Object event) {
        try {
            Object killer = getKillerMethod.invoke(event);
            if (killer instanceof Player) {
                return (Player) killer;
            }
        } catch (Exception e) {
            // Ignore
        }
        return null;
    }
    
    /**
     * Lấy drop boost value từ items của player
     */
    private double getDropBoostValue(Player player) {
        double totalBoost = 0;
        
        // Kiểm tra main hand
        ItemStack mainHand = player.getInventory().getItemInMainHand();
        totalBoost += getDropBoostFromItem(mainHand);
        
        // Kiểm tra off hand
        ItemStack offHand = player.getInventory().getItemInOffHand();
        totalBoost += getDropBoostFromItem(offHand);
        
        // Kiểm tra armor
        for (ItemStack armor : player.getInventory().getArmorContents()) {
            totalBoost += getDropBoostFromItem(armor);
        }
        
        return totalBoost;
    }
    
    /**
     * Lấy drop boost value từ một item
     */
    private double getDropBoostFromItem(ItemStack item) {
        if (item == null || item.getType().isAir()) return 0;
        
        NBTItem nbtItem = NBTItem.get(item);
        if (!nbtItem.hasTag("MMOITEMS_MYTHIC_DROP_BOOST")) return 0;
        
        return nbtItem.getDouble("MMOITEMS_MYTHIC_DROP_BOOST");
    }
    
    /**
     * Áp dụng drop boost lên drops
     */
    private void applyDropBoost(java.util.List<ItemStack> drops, double boostPercent, Player player, Entity entity) {
        try {
            for (ItemStack drop : drops) {
                // Tính toán boost chance
                double boostChance = Math.min(boostPercent, 100.0); // Cap tại 100%
                
                if (random.nextDouble() * 100 < boostChance) {
                    // Boost được kích hoạt, tăng amount của drop
                    boostDropAmount(drop, boostPercent);
                }
            }
            
            // Thông báo cho player (optional)
            if (boostPercent > 0) {
                // player.sendMessage("§a[Mythic Drop Boost] §7Tăng §a" + 
                //     String.format("%.1f", boostPercent) + "% §7drop rate!");
            }
            
        } catch (Exception e) {
            plugin.getLogger().warning("Error applying drop boost: " + e.getMessage());
        }
    }
    
    /**
     * Tăng amount của một drop
     */
    private void boostDropAmount(ItemStack drop, double boostPercent) {
        try {
            // Đơn giản hóa: chỉ tăng amount của ItemStack
            int currentAmount = drop.getAmount();
            double multiplier = 1 + (boostPercent / 100.0);
            int boostedAmount = (int) Math.round(currentAmount * multiplier);

            // Đảm bảo amount không vượt quá max stack size
            int maxStackSize = drop.getMaxStackSize();
            boostedAmount = Math.min(boostedAmount, maxStackSize);

            drop.setAmount(boostedAmount);

        } catch (Exception e) {
            plugin.getLogger().warning("Error boosting drop amount: " + e.getMessage());
        }
    }
}
