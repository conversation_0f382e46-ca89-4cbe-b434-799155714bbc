package shyrcs.Skills;

import io.lumine.mythic.lib.api.item.NBTItem;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import shyrcs.Ability.CooldownManager;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Listener xử lý Death Zone skill activation thông qua NBT tags
 * Format NBT: death_zone <damage> <time> <click_type> <cooldown>
 * Ví dụ: death_zone 20 5 right_click 30
 */
public class DeathZoneListener implements Listener {
    
    private final Plugin plugin;
    private final DeathZoneEffect deathZoneEffect;
    private final Map<UUID, Long> cooldowns = new HashMap<>();
    
    public DeathZoneListener(Plugin plugin) {
        this.plugin = plugin;
        this.deathZoneEffect = new DeathZoneEffect(plugin);
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();
        
        if (item == null || !item.hasItemMeta()) {
            return;
        }
        
        // Đọc NBT tags để tìm death_zone skill
        NBTItem nbtItem = NBTItem.get(item);
        String deathZoneConfig = findDeathZoneConfig(nbtItem);
        
        if (deathZoneConfig == null) {
            return; // Không có death_zone skill
        }
        
        // Parse config: death_zone <damage> <time> <click_type> <cooldown>
        String[] parts = deathZoneConfig.split(" ");
        if (parts.length != 5 || !parts[0].equals("death_zone")) {
            player.sendMessage("§c[Death Zone] §7Cấu hình NBT không hợp lệ! Format: death_zone <damage> <time> <click_type> <cooldown>");
            return;
        }
        
        double damage;
        int duration;
        String requiredClickType = parts[3].toLowerCase();
        int cooldownSeconds;
        
        try {
            damage = Double.parseDouble(parts[1]);
            duration = Integer.parseInt(parts[2]);
            cooldownSeconds = Integer.parseInt(parts[4]);
        } catch (NumberFormatException e) {
            player.sendMessage("§c[Death Zone] §7Tham số không hợp lệ! Damage, time và cooldown phải là số!");
            return;
        }
        
        // Validate parameters
        if (damage <= 0) {
            player.sendMessage("§c[Death Zone] §7Damage phải > 0!");
            return;
        }
        if (duration <= 0) {
            player.sendMessage("§c[Death Zone] §7Duration phải > 0!");
            return;
        }
        if (cooldownSeconds < 0) {
            player.sendMessage("§c[Death Zone] §7Cooldown phải >= 0!");
            return;
        }
        
        // Kiểm tra click type
        String actualClickType = getClickType(event.getAction(), player.isSneaking());
        if (!actualClickType.equals(requiredClickType)) {
            return; // Không phải click type đúng
        }
        
        // Cancel event để tránh các hành động khác
        event.setCancelled(true);
        
        // Kiểm tra cooldown
        UUID playerId = player.getUniqueId();
        long currentTime = System.currentTimeMillis();
        
        if (cooldowns.containsKey(playerId)) {
            long lastUse = cooldowns.get(playerId);
            long timePassed = (currentTime - lastUse) / 1000;
            
            if (timePassed < cooldownSeconds) {
                long remaining = cooldownSeconds - timePassed;
                
                // Sử dụng CooldownManager để hiển thị cooldown trên ActionBar
                CooldownManager cooldownManager = CooldownManager.getInstance();
                if (cooldownManager != null) {
                    cooldownManager.setCooldown(player, "Death Zone", (int) remaining);
                }
                
                player.sendMessage("§c[Death Zone] §7Còn §e" + remaining + "s §7cooldown!");
                return;
            }
        }
        
        // Kiểm tra bảo vệ trước khi kích hoạt
        if (!canUseDeathZone(player, player.getLocation())) {
            player.sendMessage("§c[Death Zone] §7Không thể sử dụng Death Zone tại khu vực được bảo vệ!");
            return;
        }

        // Kích hoạt Death Zone
        deathZoneEffect.activateDeathZone(player, damage, duration);
        
        // Set cooldown
        cooldowns.put(playerId, currentTime);
        CooldownManager cooldownManager = CooldownManager.getInstance();
        if (cooldownManager != null) {
            cooldownManager.setCooldown(player, "Death Zone", cooldownSeconds);
        }
        
        // Thông báo thành công
        player.sendMessage("§5[Death Zone] §7Đã kích hoạt Death Zone! Damage: §e" + damage + "HP/s §7Duration: §e" + duration + "s §7Cooldown: §e" + cooldownSeconds + "s");
    }
    
    /**
     * Tìm death_zone config trong NBT tags của item
     */
    private String findDeathZoneConfig(NBTItem nbtItem) {
        String[] possibleTags = {
            "MMOITEMS_DEATH_ZONE",
            "death_zone", 
            "DEATH_ZONE",
            "skill_death_zone",
            "SKILL_DEATH_ZONE"
        };
        
        for (String tag : possibleTags) {
            if (nbtItem.hasTag(tag)) {
                String value = nbtItem.getString(tag);
                if (value != null && value.startsWith("death_zone")) {
                    return value;
                }
            }
        }
        
        // Fallback search
        for (String key : nbtItem.getTags()) {
            if (key.toLowerCase().contains("death") || key.toLowerCase().contains("zone")) {
                String value = nbtItem.getString(key);
                if (value != null && value.startsWith("death_zone")) {
                    return value;
                }
            }
        }
        
        return null;
    }
    
    /**
     * Xác định loại click từ event
     */
    private String getClickType(Action action, boolean isSneaking) {
        String baseType;
        
        switch (action) {
            case LEFT_CLICK_AIR:
            case LEFT_CLICK_BLOCK:
                baseType = "left_click";
                break;
            case RIGHT_CLICK_AIR:
            case RIGHT_CLICK_BLOCK:
                baseType = "right_click";
                break;
            default:
                return "unknown";
        }
        
        if (isSneaking) {
            return "shift_" + baseType;
        } else {
            return baseType;
        }
    }
    
    /**
     * Kiểm tra xem player có thể sử dụng Death Zone tại location không
     */
    private boolean canUseDeathZone(Player player, Location location) {
        // Kiểm tra WorldGuard
        if (!canUseWorldGuard(player, location)) {
            return false;
        }

        // Kiểm tra Superior Skyblock
        if (!canUseSuperiorSkyblock(player, location)) {
            return false;
        }

        return true;
    }

    /**
     * Kiểm tra WorldGuard permissions
     */
    private boolean canUseWorldGuard(Player player, Location location) {
        try {
            // Kiểm tra xem WorldGuard có tồn tại không
            if (plugin.getServer().getPluginManager().getPlugin("WorldGuard") == null) {
                return true; // Không có WorldGuard = cho phép
            }

            // Sử dụng reflection để check WorldGuard regions
            Class<?> worldGuardClass = Class.forName("com.sk89q.worldguard.WorldGuard");
            Object worldGuard = worldGuardClass.getMethod("getInstance").invoke(null);

            Object platform = worldGuardClass.getMethod("getPlatform").invoke(worldGuard);
            Object regionContainer = platform.getClass().getMethod("getRegionContainer").invoke(platform);

            Object regionManager = regionContainer.getClass().getMethod("get", org.bukkit.World.class)
                .invoke(regionContainer, location.getWorld());

            if (regionManager == null) {
                return true; // Không có region manager = cho phép
            }

            // Convert location
            Class<?> bukkitAdapterClass = Class.forName("com.sk89q.worldedit.bukkit.BukkitAdapter");
            Object wgLocation = bukkitAdapterClass.getMethod("adapt", Location.class).invoke(null, location);

            // Get regions
            Object applicableRegions = regionManager.getClass().getMethod("getApplicableRegions",
                Class.forName("com.sk89q.worldedit.util.Location")).invoke(regionManager, wgLocation);

            // Nếu có regions thì kiểm tra PVP flag
            int regionCount = (Integer) applicableRegions.getClass().getMethod("size").invoke(applicableRegions);

            if (regionCount > 0) {
                // Có regions, kiểm tra PVP flag
                try {
                    Class<?> flagsClass = Class.forName("com.sk89q.worldguard.protection.flags.Flags");
                    Object pvpFlag = flagsClass.getField("PVP").get(null);

                    Object wgPlayer = bukkitAdapterClass.getMethod("adapt", org.bukkit.entity.Player.class).invoke(null, player);

                    Object pvpResult = applicableRegions.getClass().getMethod("testState",
                        Class.forName("com.sk89q.worldguard.LocalPlayer"),
                        Class.forName("com.sk89q.worldguard.protection.flags.StateFlag"))
                        .invoke(applicableRegions, wgPlayer, pvpFlag);

                    // Nếu PVP bị deny thì không cho phép Death Zone
                    if (pvpResult != null && "DENY".equals(pvpResult.toString())) {
                        return false;
                    }

                    // Nếu PVP allow hoặc không set thì cho phép Death Zone
                    return true;

                } catch (Exception flagError) {
                    // Nếu không check được PVP flag thì deny để an toàn
                    return false;
                }
            }

            return true; // Cho phép nếu không có region

        } catch (Exception e) {
            // Nếu có lỗi thì không cho phép (safe fallback)
            return false;
        }
    }

    /**
     * Kiểm tra Superior Skyblock permissions
     */
    private boolean canUseSuperiorSkyblock(Player player, Location location) {
        try {
            // Kiểm tra xem SuperiorSkyblock có tồn tại không
            if (plugin.getServer().getPluginManager().getPlugin("SuperiorSkyblock2") == null) {
                return true; // Không có SuperiorSkyblock = cho phép
            }

            // Sử dụng reflection để kiểm tra SuperiorSkyblock
            Class<?> superiorSkyblockAPIClass = Class.forName("com.bgsoftware.superiorskyblock.api.SuperiorSkyblockAPI");

            // Lấy island tại location
            Object island = superiorSkyblockAPIClass.getMethod("getIslandAt", Location.class)
                .invoke(null, location);

            if (island == null) {
                return true; // Không có island = cho phép (wilderness)
            }

            // Kiểm tra xem player có phải member của island không
            Object superiorPlayer = superiorSkyblockAPIClass.getMethod("getPlayer", java.util.UUID.class)
                .invoke(null, player.getUniqueId());

            boolean isMember = (Boolean) island.getClass().getMethod("isMember",
                Class.forName("com.bgsoftware.superiorskyblock.api.wrappers.SuperiorPlayer"))
                .invoke(island, superiorPlayer);

            return isMember; // Chỉ cho phép nếu là member của island

        } catch (Exception e) {
            // Nếu có lỗi thì cho phép (fallback)
            return true;
        }
    }

    /**
     * Cleanup khi plugin disable
     */
    public void cleanup() {
        deathZoneEffect.cleanup();
        cooldowns.clear();
    }
    
    /**
     * Lấy DeathZoneEffect instance
     */
    public DeathZoneEffect getDeathZoneEffect() {
        return deathZoneEffect;
    }
}
