name: SoulShopkeeper
version: 1.0.0
main: shyrcs.shopkeeper.SoulShopkeeperPlugin
api-version: 1.21
author: shyrcs
description: Advanced shopkeeper plugin with MMOItems support and NBT preservation
website: https://github.com/shyrcs/SoulShopkeeper

depend:
  - MMOItems

softdepend:
  - MythicLib
  - MMOCore

commands:
  soulshopkeeper:
    description: Main command for SoulShopkeeper
    usage: /soulshopkeeper <subcommand>
    aliases: [ssk, soulshop]
    permission: soulshopkeeper.admin

permissions:
  soulshopkeeper.admin:
    description: Full access to SoulShopkeeper commands
    default: op
  soulshopkeeper.create:
    description: Create new shopkeepers
    default: op
  soulshopkeeper.edit:
    description: Edit existing shopkeepers
    default: op
  soulshopkeeper.delete:
    description: Delete shopkeepers
    default: op
  soulshopkeeper.use:
    description: Use shopkeepers
    default: true
