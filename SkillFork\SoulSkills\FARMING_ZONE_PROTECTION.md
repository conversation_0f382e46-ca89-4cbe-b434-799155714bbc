# 🛡️ FARMING ZONE - PROTECTION SYSTEM

## 🎯 **Tổng Quan**

Farming Zone skill được tích hợp với hệ thống bảo vệ để đảm bảo không vi phạm quyền sở hữu:

- ✅ **WorldGuard Integration** - Không hoạt động trong regions được bảo vệ
- ✅ **Superior Skyblock Integration** - Chỉ hoạt động trên island của player
- ✅ **Block-level Protection** - Kiểm tra từng block trước khi farm
- ✅ **Reflection-based** - Không cần hard dependency
- ✅ **Graceful Fallback** - Cho phép nếu plugin không có

---

## 🔧 **Cách Hoạt Động**

### **Protection Flow:**
1. **Activation Check** → Kiểm tra vị trí player khi kích hoạt
2. **WorldGuard Check** → Kiểm tra regions tại location
3. **SuperiorSkyblock Check** → Kiểm tra island ownership
4. **Block-level Check** → Kiểm tra từng block trước khi farm
5. **Safe Farming** → Chỉ farm blocks được phép

### **Technical Implementation:**
```java
// Kiểm tra bảo vệ trước khi kích hoạt
if (!canUseFarmingZone(player, player.getLocation())) {
    player.sendMessage("§c[Farming Zone] §7Không thể sử dụng tại khu vực được bảo vệ!");
    return;
}

// Kiểm tra từng block trước khi farm
if (isMatureCrop(block) && canFarmAtLocation(block.getLocation())) {
    crops.add(block);
}
```

---

## 🌍 **WorldGuard Integration**

### **Protection Logic:**
- **No Regions** → Cho phép farming
- **Has Regions** → Không cho phép farming
- **No WorldGuard** → Cho phép farming (fallback)

### **Technical Details:**
```java
// Reflection-based WorldGuard check
Class<?> worldGuardClass = Class.forName("com.sk89q.worldguard.WorldGuard");
Object worldGuard = worldGuardClass.getMethod("getInstance").invoke(null);

// Get region manager
Object regionManager = regionContainerClass.getMethod("get", World.class)
    .invoke(regionContainer, location.getWorld());

// Check applicable regions
Object applicableRegions = regionManagerClass.getMethod("getApplicableRegions", 
    Class.forName("com.sk89q.worldedit.util.Location")).invoke(regionManager, wgLocation);

// Allow only if no regions
int regionCount = (Integer) applicableRegionsClass.getMethod("size").invoke(applicableRegions);
return regionCount == 0;
```

### **Supported Scenarios:**
- ✅ **Wilderness** - Không có region → Cho phép
- ❌ **Protected Regions** - Có region → Không cho phép
- ✅ **No WorldGuard** - Plugin không có → Cho phép

---

## 🏝️ **Superior Skyblock Integration**

### **Protection Logic:**
- **No Island** → Cho phép farming (wilderness)
- **Player's Island** → Cho phép farming
- **Other's Island** → Không cho phép farming
- **No SuperiorSkyblock** → Cho phép farming (fallback)

### **Technical Details:**
```java
// Reflection-based SuperiorSkyblock check
Class<?> superiorSkyblockAPIClass = Class.forName("com.bgsoftware.superiorskyblock.api.SuperiorSkyblockAPI");

// Get island at location
Object island = superiorSkyblockAPIClass.getMethod("getIslandAt", Location.class)
    .invoke(null, location);

if (island == null) {
    return true; // Wilderness = allow
}

// Check if player is member
Object superiorPlayer = superiorSkyblockAPIClass.getMethod("getPlayer", UUID.class)
    .invoke(null, player.getUniqueId());

boolean isMember = (Boolean) islandClass.getMethod("isMember", 
    Class.forName("com.bgsoftware.superiorskyblock.api.wrappers.SuperiorPlayer"))
    .invoke(island, superiorPlayer);

return isMember; // Only allow if member
```

### **Supported Scenarios:**
- ✅ **Wilderness** - Không có island → Cho phép
- ✅ **Own Island** - Player là member → Cho phép
- ❌ **Other's Island** - Player không phải member → Không cho phép
- ✅ **No SuperiorSkyblock** - Plugin không có → Cho phép

---

## 📊 **Protection Levels**

### **Level 1: Activation Protection**
```java
// Kiểm tra khi player kích hoạt skill
if (!canUseFarmingZone(player, player.getLocation())) {
    player.sendMessage("§c[Farming Zone] §7Không thể sử dụng tại khu vực được bảo vệ!");
    return;
}
```

**Purpose:** Ngăn kích hoạt skill tại khu vực được bảo vệ

### **Level 2: Block-level Protection**
```java
// Kiểm tra từng block trước khi farm
if (isMatureCrop(block) && canFarmAtLocation(block.getLocation())) {
    crops.add(block);
}
```

**Purpose:** Đảm bảo chỉ farm blocks được phép

---

## 🎮 **User Experience**

### **Error Messages:**
```
§c[Farming Zone] §7Không thể sử dụng Farming Zone tại khu vực được bảo vệ!
```

### **Behavior:**
1. **Protected Area** → Skill không kích hoạt + thông báo lỗi
2. **Mixed Area** → Chỉ farm blocks không được bảo vệ
3. **Safe Area** → Hoạt động bình thường

### **Visual Feedback:**
- **Red Message** → Khu vực được bảo vệ
- **Green Message** → Kích hoạt thành công
- **No Laser** → Block được bảo vệ (bỏ qua)

---

## 🔍 **Testing Scenarios**

### **WorldGuard Testing:**
```bash
# Tạo region test
/rg define test-region

# Test trong region
/farming_zone_item → Should fail

# Test ngoài region  
/farming_zone_item → Should work
```

### **SuperiorSkyblock Testing:**
```bash
# Test trên island của mình
/farming_zone_item → Should work

# Test trên island của người khác
/farming_zone_item → Should fail

# Test ở wilderness
/farming_zone_item → Should work
```

### **Mixed Protection Testing:**
```bash
# Tạo farm area với một phần protected
# Kích hoạt farming zone
# Quan sát: Chỉ farm phần không protected
```

---

## ⚡ **Performance Impact**

### **Reflection Overhead:**
- **Minimal** - Chỉ call khi cần thiết
- **Cached** - Reflection objects được cache
- **Fallback** - Graceful nếu fail

### **Check Frequency:**
- **Activation** - 1 lần khi kích hoạt skill
- **Block-level** - Mỗi block trước khi farm
- **Optimized** - Chỉ check khi có plugin

### **Memory Usage:**
- **Low** - Không store unnecessary data
- **Efficient** - Sử dụng reflection minimal
- **Clean** - Proper cleanup

---

## 🛠️ **Configuration**

### **No Configuration Needed:**
- **Auto-detect** - Tự động phát hiện plugins
- **Smart Fallback** - Cho phép nếu không có protection
- **Zero Config** - Hoạt động out-of-the-box

### **Behavior Customization:**
```java
// Có thể customize trong code nếu cần:
// - Thay đổi logic protection
// - Thêm plugins khác
// - Customize error messages
```

---

## 🐛 **Troubleshooting**

### **Skill không kích hoạt?**

**Possible Causes:**
1. **WorldGuard Region** - Đang ở trong region được bảo vệ
   - Solution: Di chuyển ra ngoài region

2. **SuperiorSkyblock Island** - Đang ở trên island của người khác
   - Solution: Về island của mình hoặc wilderness

3. **Plugin Conflict** - Conflict với protection plugins
   - Solution: Check console logs

### **Một số blocks không farm được?**

**Possible Causes:**
1. **Mixed Protection** - Một số blocks được bảo vệ
   - Expected: Chỉ farm blocks không protected

2. **Region Overlap** - Regions chồng lên nhau
   - Solution: Check region configuration

### **Debug Commands:**
```bash
# Check WorldGuard regions
/rg info

# Check SuperiorSkyblock island
/is info

# Check protection status
# (No built-in command - check behavior)
```

---

## 📈 **Best Practices**

### **For Server Admins:**
1. **Configure Regions** properly để tránh conflicts
2. **Test Protection** trước khi release
3. **Monitor Usage** để đảm bảo không abuse
4. **Backup Data** regularly

### **For Players:**
1. **Use in Safe Areas** - Wilderness hoặc own island
2. **Respect Protection** - Không cố bypass
3. **Report Issues** nếu có bugs
4. **Follow Server Rules** về farming

### **For Developers:**
1. **Test Integration** với different plugin versions
2. **Handle Edge Cases** gracefully
3. **Monitor Performance** impact
4. **Update Reflection** nếu API changes

---

## 🎉 **Kết Luận**

Protection System mang lại:

- **Safe Farming** - Không vi phạm quyền sở hữu
- **Plugin Integration** - Hoạt động với WorldGuard & SuperiorSkyblock
- **User-friendly** - Clear error messages
- **Performance Optimized** - Minimal overhead
- **Zero Configuration** - Works out-of-the-box

**Perfect for:**
- Servers với WorldGuard regions
- SuperiorSkyblock servers
- Any server cần protection
- Multi-world environments

**Protection hoàn toàn tự động và transparent!** 🛡️🌾⚡
