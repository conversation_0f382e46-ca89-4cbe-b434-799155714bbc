package shyrcs.shopkeeper.storage.data;

import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;

import java.util.*;

/**
 * Represents a SoulShopkeeper with MMOItems support
 */
public class SoulShopkeeper {
    
    private final UUID id;
    private String name;
    private String type;
    private Location location;
    private UUID ownerId;
    private final List<ShopkeeperTrade> trades;
    private boolean active;
    private long createdTime;
    private long lastModified;
    
    // Entity reference (transient)
    private transient Entity entity;
    
    public SoulShopkeeper(UUID id, String name, String type, Location location, UUID ownerId) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.location = location;
        this.ownerId = ownerId;
        this.trades = new ArrayList<>();
        this.active = true;
        this.createdTime = System.currentTimeMillis();
        this.lastModified = this.createdTime;
    }
    
    // Getters
    public UUID getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public String getType() {
        return type;
    }
    
    public Location getLocation() {
        return location;
    }
    
    public UUID getOwnerId() {
        return ownerId;
    }
    
    public List<ShopkeeperTrade> getTrades() {
        return new ArrayList<>(trades);
    }
    
    public boolean isActive() {
        return active;
    }
    
    public long getCreatedTime() {
        return createdTime;
    }
    
    public long getLastModified() {
        return lastModified;
    }
    
    public Entity getEntity() {
        return entity;
    }
    
    // Setters
    public void setName(String name) {
        this.name = name;
        updateModified();
    }
    
    public void setType(String type) {
        this.type = type;
        updateModified();
    }
    
    public void setLocation(Location location) {
        this.location = location;
        updateModified();
    }
    
    public void setOwnerId(UUID ownerId) {
        this.ownerId = ownerId;
        updateModified();
    }
    
    public void setActive(boolean active) {
        this.active = active;
        updateModified();
    }
    
    public void setEntity(Entity entity) {
        this.entity = entity;
    }
    
    // Trade management
    public void addTrade(ShopkeeperTrade trade) {
        if (trade != null && !trades.contains(trade)) {
            trades.add(trade);
            updateModified();
        }
    }
    
    public void removeTrade(ShopkeeperTrade trade) {
        if (trades.remove(trade)) {
            updateModified();
        }
    }
    
    public void removeTrade(int index) {
        if (index >= 0 && index < trades.size()) {
            trades.remove(index);
            updateModified();
        }
    }
    
    public void clearTrades() {
        if (!trades.isEmpty()) {
            trades.clear();
            updateModified();
        }
    }
    
    public ShopkeeperTrade getTrade(int index) {
        if (index >= 0 && index < trades.size()) {
            return trades.get(index);
        }
        return null;
    }
    
    public int getTradeCount() {
        return trades.size();
    }
    
    public boolean hasTrades() {
        return !trades.isEmpty();
    }
    
    // Utility methods
    private void updateModified() {
        this.lastModified = System.currentTimeMillis();
    }
    
    /**
     * Gets the EntityType for this shopkeeper
     */
    public EntityType getEntityType() {
        try {
            return EntityType.valueOf(type.toUpperCase());
        } catch (IllegalArgumentException e) {
            return EntityType.VILLAGER; // Default fallback
        }
    }
    
    /**
     * Checks if this shopkeeper is owned by the specified player
     */
    public boolean isOwnedBy(UUID playerId) {
        return ownerId != null && ownerId.equals(playerId);
    }
    
    /**
     * Checks if this shopkeeper has an owner
     */
    public boolean hasOwner() {
        return ownerId != null;
    }
    
    /**
     * Gets the maximum number of trades allowed
     */
    public int getMaxTrades() {
        // Get from config, default to 27 (3 rows in GUI)
        return shyrcs.shopkeeper.SoulShopkeeperPlugin.getInstance()
               .getConfig().getInt("shopkeeper.max-trades-per-shopkeeper", 27);
    }
    
    /**
     * Checks if more trades can be added
     */
    public boolean canAddMoreTrades() {
        int maxTrades = getMaxTrades();
        return maxTrades == -1 || trades.size() < maxTrades; // -1 means unlimited
    }
    
    /**
     * Gets enabled trades only
     */
    public List<ShopkeeperTrade> getEnabledTrades() {
        return trades.stream()
                .filter(ShopkeeperTrade::isEnabled)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * Gets the number of enabled trades
     */
    public int getEnabledTradeCount() {
        return (int) trades.stream().filter(ShopkeeperTrade::isEnabled).count();
    }
    
    /**
     * Validates this shopkeeper
     */
    public boolean isValid() {
        return id != null && 
               name != null && !name.isEmpty() && 
               type != null && !type.isEmpty() && 
               location != null && 
               location.getWorld() != null;
    }
    
    /**
     * Gets a summary string for this shopkeeper
     */
    public String getSummary() {
        return String.format("SoulShopkeeper{id=%s, name='%s', type='%s', trades=%d, active=%s}", 
                           id.toString().substring(0, 8), name, type, trades.size(), active);
    }
    
    @Override
    public String toString() {
        return getSummary();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        SoulShopkeeper that = (SoulShopkeeper) obj;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
