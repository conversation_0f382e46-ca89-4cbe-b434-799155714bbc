package shyrcs.shopkeeper.storage.database;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.logging.Logger;

/**
 * Manages database connections and operations for SoulShopkeeper
 * Supports both SQLite and MySQL with connection pooling
 */
public class DatabaseManager {
    
    private final SoulShopkeeperPlugin plugin;
    private final Logger logger;
    private HikariDataSource dataSource;
    private DatabaseType databaseType;
    
    public DatabaseManager(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
    }
    
    /**
     * Initializes the database connection
     */
    public boolean initialize() {
        String storageType = plugin.getConfig().getString("storage.type", "SQLITE").toUpperCase();
        
        try {
            switch (storageType) {
                case "SQLITE":
                    return initializeSQLite();
                case "MYSQL":
                    return initializeMySQL();
                default:
                    logger.warning("Unknown storage type: " + storageType + ". Using SQLite as fallback.");
                    return initializeSQLite();
            }
        } catch (Exception e) {
            logger.severe("Failed to initialize database: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Initializes SQLite database
     */
    private boolean initializeSQLite() {
        try {
            databaseType = DatabaseType.SQLITE;
            
            // Create data folder if it doesn't exist
            File dataFolder = plugin.getDataFolder();
            if (!dataFolder.exists()) {
                dataFolder.mkdirs();
            }
            
            String fileName = plugin.getConfig().getString("storage.sqlite.file-name", "soulshopkeeper.db");
            File dbFile = new File(dataFolder, fileName);
            
            HikariConfig config = new HikariConfig();
            config.setJdbcUrl("jdbc:sqlite:" + dbFile.getAbsolutePath());
            config.setDriverClassName("org.sqlite.JDBC");
            
            // SQLite specific settings
            config.setMaximumPoolSize(plugin.getConfig().getInt("storage.sqlite.pool-size", 10));
            config.setConnectionTimeout(plugin.getConfig().getInt("storage.sqlite.timeout", 30) * 1000L);
            config.setIdleTimeout(600000); // 10 minutes
            config.setMaxLifetime(1800000); // 30 minutes
            
            // SQLite connection properties
            config.addDataSourceProperty("journal_mode", 
                plugin.getConfig().getBoolean("storage.sqlite.wal-mode", true) ? "WAL" : "DELETE");
            config.addDataSourceProperty("synchronous", "NORMAL");
            config.addDataSourceProperty("cache_size", "10000");
            config.addDataSourceProperty("foreign_keys", "true");
            config.addDataSourceProperty("busy_timeout", "30000");
            
            dataSource = new HikariDataSource(config);
            
            logger.info("SQLite database initialized: " + dbFile.getAbsolutePath());
            return createTables();
            
        } catch (Exception e) {
            logger.severe("Failed to initialize SQLite: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Initializes MySQL database
     */
    private boolean initializeMySQL() {
        try {
            databaseType = DatabaseType.MYSQL;
            
            String host = plugin.getConfig().getString("storage.mysql.host", "localhost");
            int port = plugin.getConfig().getInt("storage.mysql.port", 3306);
            String database = plugin.getConfig().getString("storage.mysql.database", "soulshopkeeper");
            String username = plugin.getConfig().getString("storage.mysql.username", "root");
            String password = plugin.getConfig().getString("storage.mysql.password", "");
            boolean useSSL = plugin.getConfig().getBoolean("storage.mysql.use-ssl", false);
            
            HikariConfig config = new HikariConfig();
            config.setJdbcUrl(String.format("*******************************************************************************", 
                             host, port, database, useSSL));
            config.setUsername(username);
            config.setPassword(password);
            config.setDriverClassName("com.mysql.cj.jdbc.Driver");
            
            // MySQL specific settings
            config.setMaximumPoolSize(plugin.getConfig().getInt("storage.mysql.pool-size", 10));
            config.setMaxLifetime(plugin.getConfig().getLong("storage.mysql.max-lifetime", 1800000));
            config.setConnectionTimeout(plugin.getConfig().getLong("storage.mysql.connection-timeout", 20000));
            config.setIdleTimeout(600000);
            
            // MySQL connection properties
            config.addDataSourceProperty("cachePrepStmts", "true");
            config.addDataSourceProperty("prepStmtCacheSize", "250");
            config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
            config.addDataSourceProperty("useServerPrepStmts", "true");
            config.addDataSourceProperty("useLocalSessionState", "true");
            config.addDataSourceProperty("rewriteBatchedStatements", "true");
            config.addDataSourceProperty("cacheResultSetMetadata", "true");
            config.addDataSourceProperty("cacheServerConfiguration", "true");
            config.addDataSourceProperty("elideSetAutoCommits", "true");
            config.addDataSourceProperty("maintainTimeStats", "false");
            
            dataSource = new HikariDataSource(config);
            
            logger.info("MySQL database initialized: " + host + ":" + port + "/" + database);
            return createTables();
            
        } catch (Exception e) {
            logger.severe("Failed to initialize MySQL: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Creates database tables
     */
    private boolean createTables() {
        try (Connection connection = getConnection()) {
            
            // Create shopkeepers table
            String createShopkeepersTable = getCreateShopkeepersTableSQL();
            try (PreparedStatement stmt = connection.prepareStatement(createShopkeepersTable)) {
                stmt.executeUpdate();
            }
            
            // Create trades table
            String createTradesTable = getCreateTradesTableSQL();
            try (PreparedStatement stmt = connection.prepareStatement(createTradesTable)) {
                stmt.executeUpdate();
            }
            
            // Create mmo_items table
            String createMMOItemsTable = getCreateMMOItemsTableSQL();
            try (PreparedStatement stmt = connection.prepareStatement(createMMOItemsTable)) {
                stmt.executeUpdate();
            }
            
            logger.info("Database tables created successfully");
            return true;
            
        } catch (SQLException e) {
            logger.severe("Failed to create database tables: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Gets SQL for creating shopkeepers table
     */
    private String getCreateShopkeepersTableSQL() {
        String prefix = getTablePrefix();
        
        if (databaseType == DatabaseType.SQLITE) {
            return "CREATE TABLE IF NOT EXISTS " + prefix + "shopkeepers (" +
                   "id TEXT PRIMARY KEY, " +
                   "name TEXT NOT NULL, " +
                   "type TEXT NOT NULL, " +
                   "world TEXT NOT NULL, " +
                   "x REAL NOT NULL, " +
                   "y REAL NOT NULL, " +
                   "z REAL NOT NULL, " +
                   "yaw REAL NOT NULL, " +
                   "pitch REAL NOT NULL, " +
                   "owner_id TEXT, " +
                   "active INTEGER NOT NULL DEFAULT 1, " +
                   "created_time INTEGER NOT NULL, " +
                   "last_modified INTEGER NOT NULL" +
                   ")";
        } else {
            return "CREATE TABLE IF NOT EXISTS " + prefix + "shopkeepers (" +
                   "id VARCHAR(36) PRIMARY KEY, " +
                   "name VARCHAR(255) NOT NULL, " +
                   "type VARCHAR(50) NOT NULL, " +
                   "world VARCHAR(255) NOT NULL, " +
                   "x DOUBLE NOT NULL, " +
                   "y DOUBLE NOT NULL, " +
                   "z DOUBLE NOT NULL, " +
                   "yaw FLOAT NOT NULL, " +
                   "pitch FLOAT NOT NULL, " +
                   "owner_id VARCHAR(36), " +
                   "active BOOLEAN NOT NULL DEFAULT TRUE, " +
                   "created_time BIGINT NOT NULL, " +
                   "last_modified BIGINT NOT NULL" +
                   ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        }
    }
    
    /**
     * Gets SQL for creating trades table
     */
    private String getCreateTradesTableSQL() {
        String prefix = getTablePrefix();
        
        if (databaseType == DatabaseType.SQLITE) {
            return "CREATE TABLE IF NOT EXISTS " + prefix + "trades (" +
                   "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                   "shopkeeper_id TEXT NOT NULL, " +
                   "trade_index INTEGER NOT NULL, " +
                   "result_item_id TEXT NOT NULL, " +
                   "ingredient1_item_id TEXT NOT NULL, " +
                   "ingredient2_item_id TEXT, " +
                   "max_uses INTEGER NOT NULL DEFAULT -1, " +
                   "uses INTEGER NOT NULL DEFAULT 0, " +
                   "enabled INTEGER NOT NULL DEFAULT 1, " +
                   "created_time INTEGER NOT NULL, " +
                   "last_used INTEGER NOT NULL DEFAULT 0, " +
                   "FOREIGN KEY (shopkeeper_id) REFERENCES " + prefix + "shopkeepers(id) ON DELETE CASCADE" +
                   ")";
        } else {
            return "CREATE TABLE IF NOT EXISTS " + prefix + "trades (" +
                   "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                   "shopkeeper_id VARCHAR(36) NOT NULL, " +
                   "trade_index INT NOT NULL, " +
                   "result_item_id VARCHAR(255) NOT NULL, " +
                   "ingredient1_item_id VARCHAR(255) NOT NULL, " +
                   "ingredient2_item_id VARCHAR(255), " +
                   "max_uses INT NOT NULL DEFAULT -1, " +
                   "uses INT NOT NULL DEFAULT 0, " +
                   "enabled BOOLEAN NOT NULL DEFAULT TRUE, " +
                   "created_time BIGINT NOT NULL, " +
                   "last_used BIGINT NOT NULL DEFAULT 0, " +
                   "FOREIGN KEY (shopkeeper_id) REFERENCES " + prefix + "shopkeepers(id) ON DELETE CASCADE" +
                   ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        }
    }
    
    /**
     * Gets SQL for creating mmo_items table
     */
    private String getCreateMMOItemsTableSQL() {
        String prefix = getTablePrefix();
        
        if (databaseType == DatabaseType.SQLITE) {
            return "CREATE TABLE IF NOT EXISTS " + prefix + "mmo_items (" +
                   "storage_id TEXT PRIMARY KEY, " +
                   "mmo_type TEXT NOT NULL, " +
                   "mmo_id TEXT NOT NULL, " +
                   "material TEXT NOT NULL, " +
                   "amount INTEGER NOT NULL, " +
                   "display_name TEXT, " +
                   "lore TEXT, " +
                   "stored_data TEXT, " +
                   "raw_nbt TEXT, " +
                   "timestamp INTEGER NOT NULL" +
                   ")";
        } else {
            return "CREATE TABLE IF NOT EXISTS " + prefix + "mmo_items (" +
                   "storage_id VARCHAR(255) PRIMARY KEY, " +
                   "mmo_type VARCHAR(100) NOT NULL, " +
                   "mmo_id VARCHAR(255) NOT NULL, " +
                   "material VARCHAR(100) NOT NULL, " +
                   "amount INT NOT NULL, " +
                   "display_name TEXT, " +
                   "lore TEXT, " +
                   "stored_data LONGTEXT, " +
                   "raw_nbt LONGTEXT, " +
                   "timestamp BIGINT NOT NULL" +
                   ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        }
    }
    
    /**
     * Gets a database connection from the pool
     */
    public Connection getConnection() throws SQLException {
        if (dataSource == null) {
            throw new SQLException("Database not initialized");
        }
        return dataSource.getConnection();
    }
    
    /**
     * Gets the table prefix
     */
    public String getTablePrefix() {
        if (databaseType == DatabaseType.MYSQL) {
            return plugin.getConfig().getString("storage.mysql.table-prefix", "ssk_");
        }
        return ""; // SQLite doesn't need prefix usually
    }
    
    /**
     * Gets the database type
     */
    public DatabaseType getDatabaseType() {
        return databaseType;
    }
    
    /**
     * Closes the database connection pool
     */
    public void close() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            logger.info("Database connection pool closed");
        }
    }
    
    /**
     * Checks if the database is connected
     */
    public boolean isConnected() {
        if (dataSource == null) {
            return false;
        }
        
        try (Connection connection = getConnection()) {
            return connection != null && !connection.isClosed();
        } catch (SQLException e) {
            return false;
        }
    }
    
    /**
     * Database types supported
     */
    public enum DatabaseType {
        SQLITE,
        MYSQL
    }
}
