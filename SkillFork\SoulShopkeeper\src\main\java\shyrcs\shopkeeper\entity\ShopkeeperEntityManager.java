package shyrcs.shopkeeper.entity;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.entity.Villager;
import org.bukkit.metadata.FixedMetadataValue;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * Manages shopkeeper entities independently without external dependencies
 */
public class ShopkeeperEntityManager {
    
    private final SoulShopkeeperPlugin plugin;
    private final Logger logger;
    
    // Map to track spawned entities
    private final Map<UUID, Entity> spawnedEntities;
    
    // Metadata key for identifying shopkeeper entities
    private static final String METADATA_KEY = "soulshopkeeper_id";
    
    public ShopkeeperEntityManager(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.spawnedEntities = new ConcurrentHashMap<>();
    }
    
    /**
     * Spawns an entity for a shopkeeper
     */
    public Entity spawnShopkeeperEntity(SoulShopkeeper shopkeeper) {
        try {
            Location location = shopkeeper.getLocation();
            EntityType entityType = shopkeeper.getEntityType();
            
            // Spawn the entity
            Entity entity = location.getWorld().spawnEntity(location, entityType);
            
            if (entity instanceof LivingEntity) {
                LivingEntity livingEntity = (LivingEntity) entity;
                
                // Configure entity properties
                configureEntity(livingEntity, shopkeeper);
                
                // Add metadata to identify this as a shopkeeper
                entity.setMetadata(METADATA_KEY, new FixedMetadataValue(plugin, shopkeeper.getId().toString()));
                
                // Store entity reference
                spawnedEntities.put(shopkeeper.getId(), entity);
                shopkeeper.setEntity(entity);
                
                if (plugin.getConfig().getBoolean("general.debug", false)) {
                    logger.info("Spawned shopkeeper entity: " + entityType + " at " + formatLocation(location));
                }
                
                return entity;
            }
            
        } catch (Exception e) {
            logger.severe("Failed to spawn shopkeeper entity: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * Configures entity properties
     */
    private void configureEntity(LivingEntity entity, SoulShopkeeper shopkeeper) {
        // Prevent entity from despawning
        entity.setRemoveWhenFarAway(false);
        entity.setPersistent(true);

        // Disable AI completely to prevent all animations and movement
        entity.setAI(false);
        entity.setGravity(false); // No gravity - cannot be pushed
        entity.setSilent(true); // No sounds

        // Make shopkeepers completely immortal - no damage from any source
        entity.setInvulnerable(true);
        entity.setHealth(entity.getMaxHealth()); // Full health
        entity.setFireTicks(0); // No fire damage

        // Set custom name
        entity.setCustomName(shopkeeper.getName());
        entity.setCustomNameVisible(true);

        // Configure specific entity types
        if (entity instanceof Villager) {
            Villager villager = (Villager) entity;
            villager.setProfession(Villager.Profession.NONE);
            villager.setVillagerType(Villager.Type.PLAINS);
            villager.setVillagerLevel(1);

            // Prevent trading with vanilla mechanics
            villager.setRecipes(java.util.Collections.emptyList());

            // Disable villager-specific behaviors
            villager.setBreed(false);
        }

        // Configure other entity types to be completely static
        configureEntityTypeSpecific(entity);

        // Only start looking behavior if enabled in config
        if (plugin.getConfig().getBoolean("entity.look-at-players", true)) {
            startSmoothLookAtPlayerBehavior(entity);
        }

        // Start position monitoring to prevent movement
        startPositionMonitoring(entity, shopkeeper);

        // Add more entity type configurations as needed
    }
    
    /**
     * Removes a shopkeeper entity
     */
    public void removeShopkeeperEntity(UUID shopkeeperId) {
        Entity entity = spawnedEntities.remove(shopkeeperId);
        if (entity != null && !entity.isDead()) {
            entity.remove();
            
            if (plugin.getConfig().getBoolean("general.debug", false)) {
                logger.info("Removed shopkeeper entity: " + shopkeeperId.toString().substring(0, 8));
            }
        }
    }
    
    /**
     * Removes a shopkeeper entity by entity reference
     */
    public void removeShopkeeperEntity(Entity entity) {
        if (entity != null && entity.hasMetadata(METADATA_KEY)) {
            String shopkeeperIdString = entity.getMetadata(METADATA_KEY).get(0).asString();
            try {
                UUID shopkeeperId = UUID.fromString(shopkeeperIdString);
                removeShopkeeperEntity(shopkeeperId);
            } catch (IllegalArgumentException e) {
                // Invalid UUID, just remove the entity
                entity.remove();
            }
        }
    }
    
    /**
     * Checks if an entity is a shopkeeper
     */
    public boolean isShopkeeperEntity(Entity entity) {
        return entity != null && entity.hasMetadata(METADATA_KEY);
    }
    
    /**
     * Gets the shopkeeper ID from an entity
     */
    public UUID getShopkeeperId(Entity entity) {
        if (!isShopkeeperEntity(entity)) {
            return null;
        }
        
        try {
            String idString = entity.getMetadata(METADATA_KEY).get(0).asString();
            return UUID.fromString(idString);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Gets the shopkeeper associated with an entity
     */
    public SoulShopkeeper getShopkeeper(Entity entity) {
        UUID shopkeeperId = getShopkeeperId(entity);
        if (shopkeeperId != null) {
            return plugin.getDataManager().getShopkeeper(shopkeeperId);
        }
        return null;
    }
    
    /**
     * Spawns all active shopkeepers
     */
    public void spawnAllShopkeepers() {
        int spawned = 0;
        
        for (SoulShopkeeper shopkeeper : plugin.getDataManager().getAllShopkeepers()) {
            if (shopkeeper.isActive()) {
                Entity entity = spawnShopkeeperEntity(shopkeeper);
                if (entity != null) {
                    spawned++;
                }
            }
        }
        
        logger.info("Spawned " + spawned + " shopkeeper entities");
    }
    
    /**
     * Removes all shopkeeper entities
     */
    public void removeAllShopkeeperEntities() {
        int removed = 0;
        
        for (UUID shopkeeperId : new java.util.HashSet<>(spawnedEntities.keySet())) {
            removeShopkeeperEntity(shopkeeperId);
            removed++;
        }
        
        logger.info("Removed " + removed + " shopkeeper entities");
    }
    
    /**
     * Refreshes a shopkeeper entity (remove and respawn)
     */
    public void refreshShopkeeperEntity(SoulShopkeeper shopkeeper) {
        removeShopkeeperEntity(shopkeeper.getId());
        
        if (shopkeeper.isActive()) {
            spawnShopkeeperEntity(shopkeeper);
        }
    }
    
    /**
     * Gets all spawned shopkeeper entities
     */
    public Map<UUID, Entity> getSpawnedEntities() {
        return new ConcurrentHashMap<>(spawnedEntities);
    }
    
    /**
     * Cleans up invalid entities
     */
    public void cleanupInvalidEntities() {
        int cleaned = 0;
        
        for (Map.Entry<UUID, Entity> entry : new java.util.HashSet<>(spawnedEntities.entrySet())) {
            Entity entity = entry.getValue();
            UUID shopkeeperId = entry.getKey();
            
            if (entity == null || entity.isDead()) {
                spawnedEntities.remove(shopkeeperId);
                cleaned++;
            } else {
                // Check if shopkeeper still exists
                SoulShopkeeper shopkeeper = plugin.getDataManager().getShopkeeper(shopkeeperId);
                if (shopkeeper == null) {
                    entity.remove();
                    spawnedEntities.remove(shopkeeperId);
                    cleaned++;
                }
            }
        }
        
        if (cleaned > 0) {
            logger.info("Cleaned up " + cleaned + " invalid shopkeeper entities");
        }
    }
    
    /**
     * Starts the entity cleanup task
     */
    public void startCleanupTask() {
        long interval = 20 * 60 * 5; // 5 minutes
        
        Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            cleanupInvalidEntities();
        }, interval, interval);
    }
    
    /**
     * Formats a location for logging
     */
    private String formatLocation(Location location) {
        return String.format("%s: %.1f, %.1f, %.1f", 
                           location.getWorld().getName(), 
                           location.getX(), 
                           location.getY(), 
                           location.getZ());
    }
    
    /**
     * Configures entity type specific behaviors
     */
    private void configureEntityTypeSpecific(LivingEntity entity) {
        // Disable specific behaviors for different entity types
        if (entity instanceof org.bukkit.entity.Zombie) {
            // Disable zombie attack animations and sounds
            entity.setSilent(true);
        } else if (entity instanceof org.bukkit.entity.Skeleton) {
            // Disable skeleton bow animations
            entity.setSilent(true);
        } else if (entity instanceof org.bukkit.entity.Creeper) {
            org.bukkit.entity.Creeper creeper = (org.bukkit.entity.Creeper) entity;
            creeper.setPowered(false);
            entity.setSilent(true);
        }
        // Add more entity types as needed
    }

    /**
     * Starts smooth AI behavior for looking at nearby players
     */
    private void startSmoothLookAtPlayerBehavior(LivingEntity entity) {
        Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (entity == null || entity.isDead()) {
                return;
            }

            // Find nearest player within 8 blocks
            Player nearestPlayer = null;
            double minDistance = 8.0;

            for (Player player : entity.getWorld().getPlayers()) {
                double distance = player.getLocation().distance(entity.getLocation());
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestPlayer = player;
                }
            }

            // Look at the nearest player smoothly
            if (nearestPlayer != null) {
                Location playerLoc = nearestPlayer.getEyeLocation();
                Location entityLoc = entity.getEyeLocation();

                // Calculate direction vector
                double dx = playerLoc.getX() - entityLoc.getX();
                double dy = playerLoc.getY() - entityLoc.getY();
                double dz = playerLoc.getZ() - entityLoc.getZ();

                // Calculate yaw and pitch
                double yaw = Math.atan2(-dx, dz) * 180.0 / Math.PI;
                double pitch = Math.atan2(-dy, Math.sqrt(dx * dx + dz * dz)) * 180.0 / Math.PI;

                // Get current rotation
                Location currentLoc = entity.getLocation();
                float currentYaw = currentLoc.getYaw();
                float currentPitch = currentLoc.getPitch();

                // Smooth rotation - only rotate if difference is significant
                float yawDiff = (float) yaw - currentYaw;
                float pitchDiff = (float) pitch - currentPitch;

                // Normalize yaw difference
                while (yawDiff > 180) yawDiff -= 360;
                while (yawDiff < -180) yawDiff += 360;

                // Only update if difference is significant (reduce jittery movement)
                if (Math.abs(yawDiff) > 5 || Math.abs(pitchDiff) > 5) {
                    // Smooth interpolation
                    float newYaw = currentYaw + yawDiff * 0.1f;
                    float newPitch = currentPitch + pitchDiff * 0.1f;

                    // Create new location with same position but updated rotation
                    Location newLoc = currentLoc.clone();
                    newLoc.setYaw(newYaw);
                    newLoc.setPitch(newPitch);

                    // Teleport to update rotation without moving position
                    entity.teleport(newLoc);
                }
            }

        }, 20L, 20L); // Start after 1 second, repeat every 1 second (smoother)
    }

    /**
     * Monitors entity position and prevents movement
     */
    private void startPositionMonitoring(LivingEntity entity, SoulShopkeeper shopkeeper) {
        // Store the original spawn location
        Location originalLocation = shopkeeper.getLocation().clone();

        Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (entity == null || entity.isDead()) {
                return;
            }

            Location currentLocation = entity.getLocation();

            // Check if entity has moved from original position
            if (currentLocation.distance(originalLocation) > 0.1) {
                // Teleport back to original position
                Location resetLocation = originalLocation.clone();
                resetLocation.setYaw(currentLocation.getYaw()); // Keep current rotation
                resetLocation.setPitch(currentLocation.getPitch());

                entity.teleport(resetLocation);
            }

        }, 5L, 5L); // Check every 0.25 seconds for immediate correction
    }

    /**
     * Gets the shopkeeper ID from an entity
     */
    public java.util.UUID getShopkeeperIdFromEntity(Entity entity) {
        if (!isShopkeeperEntity(entity)) {
            return null;
        }

        try {
            String idString = entity.getMetadata("soulshopkeeper_id").get(0).asString();
            return java.util.UUID.fromString(idString);
        } catch (Exception e) {
            return null;
        }
    }



    /**
     * Shutdown cleanup
     */
    public void shutdown() {
        removeAllShopkeeperEntities();
        spawnedEntities.clear();
    }
}
