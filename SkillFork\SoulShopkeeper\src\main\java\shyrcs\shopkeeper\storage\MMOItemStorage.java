package shyrcs.shopkeeper.storage;

import org.bukkit.inventory.ItemStack;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.YamlConfiguration;

import net.Indyuce.mmoitems.MMOItems;
import net.Indyuce.mmoitems.api.Type;
import net.Indyuce.mmoitems.api.item.mmoitem.MMOItem;
import net.Indyuce.mmoitems.api.item.build.ItemStackBuilder;
import net.Indyuce.mmoitems.api.item.template.MMOItemTemplate;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.StoredMMOItem;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * Handles storage and retrieval of MMOItems with NBT preservation
 * This class ensures that MMOItems maintain their NBT data even after server reloads
 */
public class MMOItemStorage {
    
    private final SoulShopkeeperPlugin plugin;
    private final Logger logger;
    
    // Cache for stored MMOItems
    private final Map<String, StoredMMOItem> itemCache;
    
    // Cache for ItemStacks to improve performance
    private final Map<String, ItemStack> stackCache;
    
    public MMOItemStorage(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.itemCache = new ConcurrentHashMap<>();
        this.stackCache = new ConcurrentHashMap<>();
    }
    
    /**
     * Stores an item (MMOItem or vanilla) with NBT data preserved
     * @param itemStack The ItemStack to store (MMOItem or vanilla item)
     * @return Unique identifier for the stored item
     */
    public String storeMMOItem(ItemStack itemStack) {
        if (itemStack == null || itemStack.getType().isAir()) {
            return null;
        }
        
        try {
            // Generate unique ID for this item
            String itemId = generateItemId();
            
            // Create StoredMMOItem with all necessary data
            StoredMMOItem storedItem = StoredMMOItem.fromItemStack(itemStack);
            
            if (storedItem != null) {
                // Cache the item
                itemCache.put(itemId, storedItem);
                stackCache.put(itemId, itemStack.clone());
                
                if (plugin.getConfig().getBoolean("general.debug", false)) {
                    logger.info("Stored MMOItem with ID: " + itemId + 
                               " (Type: " + storedItem.getType() + ", ID: " + storedItem.getItemId() + ")");
                }
                
                return itemId;
            }
        } catch (Exception e) {
            logger.severe("Failed to store MMOItem: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * Retrieves a stored MMOItem and reconstructs it with preserved NBT data
     * @param storageId The unique identifier of the stored item
     * @return The reconstructed ItemStack or null if not found
     */
    public ItemStack retrieveMMOItem(String storageId) {
        if (storageId == null || storageId.isEmpty()) {
            return null;
        }
        
        try {
            // Check cache first
            if (stackCache.containsKey(storageId)) {
                ItemStack cached = stackCache.get(storageId);
                if (cached != null) {
                    return cached.clone();
                }
            }
            
            // Get from storage
            StoredMMOItem storedItem = itemCache.get(storageId);
            if (storedItem == null) {
                return null;
            }
            
            // Reconstruct the ItemStack
            ItemStack reconstructed = reconstructMMOItem(storedItem);
            
            if (reconstructed != null) {
                // Update cache
                stackCache.put(storageId, reconstructed.clone());
                
                if (plugin.getConfig().getBoolean("general.debug", false)) {
                    logger.info("Retrieved MMOItem with ID: " + storageId);
                }
            }
            
            return reconstructed;
            
        } catch (Exception e) {
            logger.severe("Failed to retrieve MMOItem with ID " + storageId + ": " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * Reconstructs an MMOItem from stored data
     * @param storedItem The stored item data
     * @return The reconstructed ItemStack
     */
    public ItemStack reconstructMMOItem(StoredMMOItem storedItem) {
        try {
            // Check if it's a vanilla item
            if ("VANILLA".equals(storedItem.getType())) {
                return reconstructVanillaItem(storedItem);
            }

            // Get MMOItem type
            Type type = Type.get(storedItem.getType());
            if (type == null) {
                logger.warning("Unknown MMOItem type: " + storedItem.getType());
                return reconstructVanillaItem(storedItem); // Fallback to vanilla
            }
            
            // Get template
            MMOItemTemplate template = MMOItems.plugin.getTemplates().getTemplate(type, storedItem.getItemId());
            if (template == null) {
                logger.warning("MMOItem template not found: " + storedItem.getType() + "." + storedItem.getItemId());
                return null;
            }
            
            // Build the item with preserved data
            MMOItem mmoItem = template.newBuilder().build();
            
            // Apply stored modifications
            if (storedItem.hasStoredData()) {
                applyStoredModifications(mmoItem, storedItem);
            }
            
            // Build final ItemStack
            ItemStack result = mmoItem.newBuilder().build();
            
            // Apply any additional NBT data that might have been lost
            if (storedItem.hasRawNBT()) {
                result = applyRawNBT(result, storedItem.getRawNBT());
            }
            
            return result;
            
        } catch (Exception e) {
            logger.warning("Failed to reconstruct MMOItem, falling back to vanilla: " + e.getMessage());
            return reconstructVanillaItem(storedItem);
        }
    }

    /**
     * Reconstructs a vanilla item from stored data
     */
    private ItemStack reconstructVanillaItem(StoredMMOItem storedItem) {
        try {
            // Get material from stored ID
            org.bukkit.Material material = org.bukkit.Material.valueOf(storedItem.getItemId());
            ItemStack item = new ItemStack(material, storedItem.getAmount());

            // Apply stored metadata
            if (storedItem.getDisplayName() != null || !storedItem.getLore().isEmpty()) {
                org.bukkit.inventory.meta.ItemMeta meta = item.getItemMeta();
                if (meta != null) {
                    if (storedItem.getDisplayName() != null) {
                        meta.setDisplayName(storedItem.getDisplayName());
                    }
                    if (!storedItem.getLore().isEmpty()) {
                        meta.setLore(storedItem.getLore());
                    }
                    item.setItemMeta(meta);
                }
            }

            return item;

        } catch (Exception e) {
            logger.severe("Failed to reconstruct vanilla item: " + e.getMessage());
            // Ultimate fallback
            return new ItemStack(org.bukkit.Material.STONE, 1);
        }
    }
    
    /**
     * Applies stored modifications to an MMOItem
     */
    private void applyStoredModifications(MMOItem mmoItem, StoredMMOItem storedItem) {
        // This method would apply any stored stat modifications, enchantments, etc.
        // Implementation depends on what specific data needs to be preserved
        
        Map<String, Object> storedData = storedItem.getStoredData();
        if (storedData != null) {
            // Apply stored stats, enchantments, etc.
            // This is where you would restore any custom modifications
        }
    }
    
    /**
     * Applies raw NBT data to an ItemStack
     */
    private ItemStack applyRawNBT(ItemStack itemStack, String rawNBT) {
        // This method would apply raw NBT data if needed
        // Implementation depends on your specific NBT preservation needs
        return itemStack;
    }
    
    /**
     * Generates a unique identifier for stored items
     */
    private String generateItemId() {
        return "mmo_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
    
    /**
     * Checks if an item is a valid MMOItem
     */
    public boolean isMMOItem(ItemStack itemStack) {
        if (itemStack == null || itemStack.getType().isAir()) {
            return false;
        }

        return MMOItems.getTypeName(itemStack) != null && MMOItems.getID(itemStack) != null;
    }
    
    /**
     * Gets MMOItem information from an ItemStack
     */
    public String getMMOItemInfo(ItemStack itemStack) {
        if (!isMMOItem(itemStack)) {
            return null;
        }

        String type = MMOItems.getTypeName(itemStack);
        String id = MMOItems.getID(itemStack);

        return type + "." + id;
    }
    
    /**
     * Clears the cache
     */
    public void clearCache() {
        itemCache.clear();
        stackCache.clear();
        logger.info("MMOItem cache cleared");
    }
    
    /**
     * Gets cache statistics
     */
    public Map<String, Integer> getCacheStats() {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("cached_items", itemCache.size());
        stats.put("cached_stacks", stackCache.size());
        return stats;
    }
    
    /**
     * Saves all cached items to persistent storage
     */
    public void saveToStorage(ConfigurationSection section) {
        for (Map.Entry<String, StoredMMOItem> entry : itemCache.entrySet()) {
            String key = entry.getKey();
            StoredMMOItem item = entry.getValue();
            
            ConfigurationSection itemSection = section.createSection(key);
            item.saveToConfig(itemSection);
        }
    }
    
    /**
     * Loads items from persistent storage
     */
    public void loadFromStorage(ConfigurationSection section) {
        if (section == null) return;
        
        for (String key : section.getKeys(false)) {
            ConfigurationSection itemSection = section.getConfigurationSection(key);
            if (itemSection != null) {
                StoredMMOItem item = StoredMMOItem.loadFromConfig(itemSection);
                if (item != null) {
                    itemCache.put(key, item);
                }
            }
        }
        
        logger.info("Loaded " + itemCache.size() + " MMOItems from storage");
    }
}
